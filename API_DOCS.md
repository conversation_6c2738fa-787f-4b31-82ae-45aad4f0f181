# 🔧 API Dokumentácia - AirCursor Assistant

## 📋 Prehľad

Táto dokumentácia popisuje hlavné triedy a API pre vý<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcú rozšíriť alebo integrovať AirCursor Assistant.

## 🏗️ Architektúra

```
main.py
├── ModernGUI (Tkinter GUI)
├── AirCursor (Hand tracking)
├── Cursor (Voice commands)
└── ConfigManager (Configuration)
```

## 📚 Hlavné triedy

### AirCursor

**Súbor:** `air_cursor.py`

Trieda pre sledovanie ruky a ovládanie kurzora pomocí MediaPipe.

```python
class AirCursor:
    def __init__(self, config_manager=None)
    def process_frame(self, frame) -> tuple | None
    def update_cursor(self, finger_pos)
    def auto_calibrate(self, frame)
    def load_calibration_points()
    def save_calibration_points()
```

**<PERSON>ľ<PERSON><PERSON><PERSON><PERSON> metódy:**

#### `process_frame(frame)`
- **Vstup:** OpenCV frame (numpy array)
- **Výstup:** <PERSON><PERSON> (x, y) pozície prsta alebo None
- **Popis:** Spracuje video frame a vráti pozíciu špičky ukazováka

#### `update_cursor(finger_pos)`
- **Vstup:** Tuple (x, y) pozície prsta
- **Výstup:** None
- **Popis:** Aktualizuje pozíciu kurzora na obrazovke

#### `auto_calibrate(frame)`
- **Vstup:** OpenCV frame
- **Výstup:** None
- **Popis:** Vykoná automatickú kalibráciu pre mapovanie gest

**Príklad použitia:**
```python
air_cursor = AirCursor()
cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    finger_pos = air_cursor.process_frame(frame)
    if finger_pos and air_cursor.calibrated:
        air_cursor.update_cursor(finger_pos)
```

### Cursor

**Súbor:** `voice_commands.py`

Trieda pre spracovanie hlasových príkazov a ovládanie systému.

```python
class Cursor:
    def __init__(self, config_path="config.json")
    def click()
    def right_click()
    def scroll_up()
    def scroll_down()
    def volume_set(value: int)
    def open_webpage(url: str) -> bool
    def open_application(app_name: str) -> bool
```

**Kľúčové metódy:**

#### `click()`, `right_click()`, `double_click()`
- **Vstup:** None
- **Výstup:** None
- **Popis:** Vykonajú príslušné kliknutia myši

#### `volume_set(value)`
- **Vstup:** int (0-100)
- **Výstup:** None
- **Popis:** Nastaví hlasitosť systému

#### `open_webpage(url)`
- **Vstup:** string URL
- **Výstup:** bool (úspech/neúspech)
- **Popis:** Otvorí webovú stránku v predvolenom prehliadači

**Príklad použitia:**
```python
cursor = Cursor()
cursor.click()  # Kliknutie
cursor.volume_set(50)  # Hlasitosť na 50%
cursor.open_webpage("https://google.com")
```

### GeminiAPI

**Súbor:** `deepseek_integration.py`

Trieda pre integráciu s Gemini AI.

```python
class GeminiAPI:
    def __init__(self, config_manager=None)
    def process_command(self, command: str) -> Dict[str, str]
```

**Kľúčové metódy:**

#### `process_command(command)`
- **Vstup:** string (hlasový príkaz)
- **Výstup:** Dict s kľúčmi "message", "status"
- **Popis:** Spracuje príkaz cez Gemini AI

**Príklad použitia:**
```python
gemini = GeminiAPI()
response = gemini.process_command("Čo je Python?")
if response["status"] == "success":
    print(response["message"])
```

### ConfigManager

**Súbor:** `config_manager.py`

Trieda pre správu konfigurácie a environment variables.

```python
class ConfigManager:
    def __init__(self, config_file='config.json')
    def get(self, *keys, default=None)
    def load_config()
```

**Kľúčové metódy:**

#### `get(*keys, default=None)`
- **Vstup:** Kľúče pre vnorené hodnoty, predvolená hodnota
- **Výstup:** Hodnota z konfigurácie alebo default
- **Popis:** Získa hodnotu z konfiguračného súboru

**Príklad použitia:**
```python
config = ConfigManager()
camera_id = config.get("camera", "device_id", default=0)
sensitivity = config.get("cursor", "sensitivity_x", default=1.0)
```

## 🔌 Rozšírenie funkcionalít

### Pridanie nového hlasového príkazu

1. **Pridajte príkaz do config.json:**
```json
{
    "voice": {
        "commands": {
            "new_command": ["nový príkaz", "alternatíva"]
        }
    }
}
```

2. **Implementujte funkciu v Cursor triede:**
```python
def new_action(self):
    """Implementácia novej akcie."""
    # Váš kód tu
    pass
```

3. **Pridajte mapovanie v __init__:**
```python
self.command_map["new_command"] = (self.new_action, "Akcia vykonaná.")
```

### Pridanie novej kalibračnej metódy

```python
class CustomAirCursor(AirCursor):
    def custom_calibrate(self, frame):
        """Vlastná kalibračná metóda."""
        # Implementácia vlastnej kalibrácie
        pass
```

### Integrácia s vlastným AI modelom

```python
class CustomAI:
    def process_command(self, command: str) -> Dict[str, str]:
        """Vlastné spracovanie príkazov."""
        # Implementácia vlastného AI
        return {"message": "odpoveď", "status": "success"}

# Použitie v voice_commands.py
cursor.custom_ai = CustomAI()
```

## 🎛️ Konfiguračné možnosti

### Hand Tracking nastavenia
```json
{
    "hand_tracking": {
        "max_hands": 1,                    // Počet rúk (1-2)
        "detection_confidence": 0.7,       // Presnosť detekcie (0.0-1.0)
        "tracking_confidence": 0.5,        // Presnosť sledovania (0.0-1.0)
        "static_image_mode": false         // Statický mód (true/false)
    }
}
```

### Cursor nastavenia
```json
{
    "cursor": {
        "failsafe": true,                  // PyAutoGUI failsafe
        "smoothing": 0.5,                  // Vyhladzovanie (0.0-1.0)
        "sensitivity_x": 1.3,              // X citlivosť
        "sensitivity_y": 1.3,              // Y citlivosť
        "step_size": 10                    // Veľkosť kroku
    }
}
```

### Gemini AI nastavenia
```json
{
    "gemini": {
        "model": "gemini-2.0-flash",       // Model name
        "timeout": 30,                     // Timeout v sekundách
        "max_tokens": 1000                 // Max tokeny (voliteľné)
    }
}
```

## 🔄 Event systém

### Queue komunikácia

Aplikácia používa Queue pre komunikáciu medzi vláknami:

```python
# Odoslanie TTS správy
queue.put(("tts", "Text na prehranie"))

# Odoslanie STT správy  
queue.put(("stt", "Rozpoznaný text"))

# Spracovanie v GUI
while not queue.empty():
    msg_type, msg = queue.get_nowait()
    if msg_type == "tts":
        # Prehrať text
        pass
```

## 🧪 Testovanie

### Unit testy

```python
import unittest
from air_cursor import AirCursor

class TestAirCursor(unittest.TestCase):
    def setUp(self):
        self.air_cursor = AirCursor()
    
    def test_calibration(self):
        # Test kalibrácie
        pass
    
    def test_finger_detection(self):
        # Test detekcie prsta
        pass
```

### Integračné testy

```python
def test_voice_command_integration():
    cursor = Cursor()
    # Test hlasových príkazov
    pass
```

## 📊 Logging a debugging

### Nastavenie logovania

```python
import logging

# Základné nastavenie
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Pre debug
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
```

### Debug informácie

```python
# V AirCursor
logger.debug(f"Finger position: {finger_pos}")
logger.debug(f"Cursor moved to: ({cursor_x}, {cursor_y})")

# V voice_commands
logger.info(f"Command recognized: {command}")
logger.debug(f"Action executed: {action_name}")
```

## 🔒 Bezpečnosť

### API kľúče
- Nikdy nekódujte API kľúče priamo do kódu
- Používajte environment variables alebo .env súbory
- Pridajte .env do .gitignore

### Validácia vstupov
```python
def safe_volume_set(self, value: int):
    """Bezpečné nastavenie hlasitosti s validáciou."""
    if not isinstance(value, int) or not 0 <= value <= 100:
        raise ValueError("Volume must be integer between 0-100")
    self.volume_set(value)
```

---

**Pre ďalšie otázky vytvorte Issue na GitHub alebo pozrite zdrojový kód.**
