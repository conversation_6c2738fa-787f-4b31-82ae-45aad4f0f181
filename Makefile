# AirCursor Assistant - Makefile
# Univerzálny build systém pre všetky platformy

# Detekcia operačného systému
ifeq ($(OS),Windows_NT)
    DETECTED_OS := Windows
    PYTHON := python
    PIP := pip
    VENV_ACTIVATE := venv\Scripts\activate.bat
    VENV_PYTHON := venv\Scripts\python.exe
    VENV_PIP := venv\Scripts\pip.exe
else
    DETECTED_OS := $(shell uname -s)
    # Prefer python3.12 if available, otherwise use python3
    PYTHON := $(shell command -v python3.12 2>/dev/null || echo python3)
    PIP := $(shell command -v pip3.12 2>/dev/null || echo pip3)
    VENV_ACTIVATE := venv/bin/activate
    VENV_PYTHON := venv/bin/python
    VENV_PIP := venv/bin/pip
endif

# Farby pre výstup
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
BLUE := \033[34m
BOLD := \033[1m
RESET := \033[0m

# Predvolený cieľ
.DEFAULT_GOAL := help

# Pomocné funkcie
define print_status
	@echo "$(BLUE)[INFO]$(RESET) $(1)"
endef

define print_success
	@echo "$(GREEN)[SUCCESS]$(RESET) $(1)"
endef

define print_warning
	@echo "$(YELLOW)[WARNING]$(RESET) $(1)"
endef

define print_error
	@echo "$(RED)[ERROR]$(RESET) $(1)"
endef

.PHONY: help
help: ## Zobrazí túto nápovedu
	@echo "$(BOLD)🎯 AirCursor Assistant - Makefile Commands$(RESET)"
	@echo "=================================================="
	@echo ""
	@echo "$(BLUE)📦 Inštalácia a setup:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(install|setup|init)"
	@echo ""
	@echo "$(BLUE)🚀 Spustenie:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(run|start)"
	@echo ""
	@echo "$(BLUE)🧪 Testovanie a kvalita:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(test|lint|format)"
	@echo ""
	@echo "$(BLUE)🔧 Údržba:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(update|clean|backup)"
	@echo ""
	@echo "$(BLUE)📚 Dokumentácia:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(docs|readme)"

# =============================================================================
# Inštalácia a setup
# =============================================================================

.PHONY: install
install: ## Kompletná inštalácia (systém + Python + setup)
	$(call print_status,"Spúšťam kompletnú inštaláciu...")
	$(PYTHON) install.py
	$(call print_success,"Inštalácia dokončená!")

.PHONY: install-system
install-system: ## Inštaluje iba systémové závislosti
	$(call print_status,"Inštalujem systémové závislosti...")
	$(PYTHON) package_manager.py install --system-only

.PHONY: install-python
install-python: venv ## Inštaluje iba Python závislosti
	$(call print_status,"Inštalujem Python závislosti...")
	$(VENV_PIP) install -r requirements.txt
	$(call print_success,"Python závislosti nainštalované!")

.PHONY: venv
venv: ## Vytvorí virtual environment
	$(call print_status,"Vytváram virtual environment...")
	$(PYTHON) -m venv venv
	$(call print_success,"Virtual environment vytvorený!")

.PHONY: setup
setup: ## Nastavenie environment variables
	$(call print_status,"Spúšťam setup environment...")
	$(PYTHON) setup_env.py

.PHONY: init
init: install setup ## Inicializácia projektu (install + setup)
	$(call print_success,"Projekt inicializovaný!")

# =============================================================================
# Spustenie
# =============================================================================

.PHONY: run
run: ## Spustí aplikáciu
	$(call print_status,"Spúšťam AirCursor Assistant...")
ifeq ($(DETECTED_OS),Windows)
	$(VENV_PYTHON) main.py
else
	source $(VENV_ACTIVATE) && python main.py
endif

.PHONY: run-dev
run-dev: ## Spustí aplikáciu v debug móde
	$(call print_status,"Spúšťam v debug móde...")
ifeq ($(DETECTED_OS),Windows)
	set DEBUG=true && $(VENV_PYTHON) main.py
else
	DEBUG=true source $(VENV_ACTIVATE) && python main.py
endif

.PHONY: start
start: run ## Alias pre run

# =============================================================================
# Testovanie a kvalita kódu
# =============================================================================

.PHONY: test
test: ## Spustí všetky testy
	$(call print_status,"Spúšťam testy...")
	$(VENV_PYTHON) -m pytest tests/ -v

.PHONY: test-quick
test-quick: ## Spustí rýchle testy
	$(call print_status,"Spúšťam rýchle testy...")
	$(VENV_PYTHON) -m pytest tests/ -v -m "not slow"

.PHONY: lint
lint: ## Kontrola kvality kódu
	$(call print_status,"Kontrolujem kvalitu kódu...")
	$(VENV_PYTHON) -m flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	$(VENV_PYTHON) -m flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

.PHONY: format
format: ## Formátuje kód pomocou black
	$(call print_status,"Formátujem kód...")
	$(VENV_PYTHON) -m black . --line-length 100
	$(call print_success,"Kód naformátovaný!")

.PHONY: type-check
type-check: ## Kontrola typov pomocou mypy
	$(call print_status,"Kontrolujem typy...")
	$(VENV_PYTHON) -m mypy . --ignore-missing-imports

# =============================================================================
# Údržba
# =============================================================================

.PHONY: update
update: ## Aktualizuje všetky závislosti
	$(call print_status,"Aktualizujem závislosti...")
	$(PYTHON) package_manager.py update
	$(call print_success,"Závislosti aktualizované!")

.PHONY: update-system
update-system: ## Aktualizuje systémové balíčky
	$(call print_status,"Aktualizujem systémové balíčky...")
	$(PYTHON) package_manager.py update --system-only

.PHONY: update-python
update-python: ## Aktualizuje Python balíčky
	$(call print_status,"Aktualizujem Python balíčky...")
	$(PYTHON) package_manager.py update --python-only

.PHONY: clean
clean: ## Vyčistí dočasné súbory a cache
	$(call print_status,"Čistím dočasné súbory...")
	$(PYTHON) package_manager.py cleanup
	$(call print_success,"Cleanup dokončený!")

.PHONY: clean-all
clean-all: clean ## Vyčistí všetko vrátane venv
	$(call print_status,"Odstraňujem virtual environment...")
ifeq ($(DETECTED_OS),Windows)
	if exist venv rmdir /s /q venv
else
	rm -rf venv
endif
	$(call print_success,"Kompletný cleanup dokončený!")

.PHONY: backup
backup: ## Vytvorí zálohu konfigurácie
	$(call print_status,"Vytváram zálohu...")
	@mkdir -p backups
	@cp config.json backups/config_$(shell date +%Y%m%d_%H%M%S).json
	@if [ -f .env ]; then cp .env backups/env_$(shell date +%Y%m%d_%H%M%S).backup; fi
	@if [ -f calibration.json ]; then cp calibration.json backups/calibration_$(shell date +%Y%m%d_%H%M%S).json; fi
	$(call print_success,"Záloha vytvorená v priečinku backups/")

# =============================================================================
# Dokumentácia
# =============================================================================

.PHONY: docs
docs: ## Generuje dokumentáciu
	$(call print_status,"Generujem dokumentáciu...")
	@echo "Dokumentácia je už vytvorená v README.md, USER_GUIDE.md a API_DOCS.md"
	$(call print_success,"Dokumentácia je aktuálna!")

.PHONY: readme
readme: ## Zobrazí README
	@cat README.md

# =============================================================================
# Diagnostika
# =============================================================================

.PHONY: info
info: ## Zobrazí informácie o systéme
	@echo "$(BOLD)🔍 Systémové informácie$(RESET)"
	@echo "================================"
	@echo "OS: $(DETECTED_OS)"
	@echo "Python: $(shell $(PYTHON) --version 2>/dev/null || echo 'Nenájdený')"
	@echo "Pip: $(shell $(PIP) --version 2>/dev/null || echo 'Nenájdený')"
	@echo "Virtual env: $(shell test -d venv && echo 'Existuje' || echo 'Neexistuje')"
	@echo "Git: $(shell git --version 2>/dev/null || echo 'Nenájdený')"
	@echo ""
	@echo "$(BOLD)📁 Súbory projektu$(RESET)"
	@echo "==================="
	@echo "config.json: $(shell test -f config.json && echo '✓' || echo '✗')"
	@echo ".env: $(shell test -f .env && echo '✓' || echo '✗')"
	@echo "requirements.txt: $(shell test -f requirements.txt && echo '✓' || echo '✗')"
	@echo "calibration.json: $(shell test -f calibration.json && echo '✓' || echo '✗')"

.PHONY: check
check: ## Kontrola stavu projektu
	$(call print_status,"Kontrolujem stav projektu...")
	@$(PYTHON) -c "import sys; print(f'Python verzia: {sys.version}')"
	@$(PYTHON) setup_env.py
	$(call print_success,"Kontrola dokončená!")

# =============================================================================
# Aliasy
# =============================================================================

.PHONY: i
i: install ## Alias pre install

.PHONY: r
r: run ## Alias pre run

.PHONY: t
t: test ## Alias pre test

.PHONY: c
c: clean ## Alias pre clean

.PHONY: u
u: update ## Alias pre update
