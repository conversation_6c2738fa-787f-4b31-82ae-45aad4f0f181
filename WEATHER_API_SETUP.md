# 🌤️ Nastavenie Weather API

## 📋 Krok za krokom návod

### 1. **Registrácia na OpenWeatherMap**

1. **Choďte na:** https://openweathermap.org/api
2. **Kliknite na:** "Sign Up" (Registrácia)
3. **Vyplňte formulár:**
   - Email
   - Heslo
   - Meno/Priezvisko
4. **Potvrďte email** cez link v emaili

### 2. **Získanie API kľúča**

1. **Prihláste sa** na https://openweathermap.org
2. **Choďte na:** "My API keys" (v profile)
3. **Skopírujte** váš API kľúč (vyzerá ako: `a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`)

### 3. **Nastavenie API kľúča**

**Metóda 1: Systémová premenná (Odporúčané)**
```bash
# Windows (PowerShell)
$env:OPENWEATHER_API_KEY="váš_api_kľúč_tu"

# Windows (CMD)
set OPENWEATHER_API_KEY=váš_api_kľúč_tu

# Linux/Mac
export OPENWEATHER_API_KEY="váš_api_kľúč_tu"
```

**Metóda 2: Konfiguračný súbor**
```json
// weather_config.json
{
  "weather_api": {
    "api_key": "váš_api_kľúč_tu"
  }
}
```

**Metóda 3: Plugin konfigurácia**
```json
// plugins/plugins_config.json
{
  "plugins": [
    {
      "name": "real_weather_plugin",
      "enabled": true,
      "config": {
        "openweather_api_key": "váš_api_kľúč_tu",
        "default_city": "Bratislava"
      }
    }
  ]
}
```

### 4. **Inštalácia závislostí**

```bash
# Inštalácia requests knižnice
pip install requests

# Alebo ak máte requirements.txt
pip install -r requirements.txt
```

### 5. **Test API kľúča**

```bash
# Test real weather pluginu
python plugins/real_weather_plugin.py

# Test API kľúča priamo
python -c "
import requests
api_key = 'váš_api_kľúč_tu'
url = f'http://api.openweathermap.org/data/2.5/weather?q=Bratislava&appid={api_key}&units=metric&lang=sk'
response = requests.get(url)
print('✅ API kľúč funguje!' if response.status_code == 200 else '❌ API kľúč nefunguje')
print(response.json() if response.status_code == 200 else response.text)
"
```

## 🆓 Bezplatné limity

**OpenWeatherMap Free Plan:**
- ✅ **1,000 volaní/deň**
- ✅ **Aktuálne počasie**
- ✅ **5-dňová predpoveď**
- ✅ **Historické dáta (1 deň)**
- ❌ Minutové predpovede
- ❌ Klimatické dáta

**Pre väčšinu používateľov je free plan dostačujúci!**

## 🔧 Alternatívne API služby

### **WeatherAPI.com**
- 🆓 **1 milión volaní/mesiac**
- 📍 **Registrácia:** https://www.weatherapi.com/signup.aspx
- 🔑 **API kľúč:** Zadarmo po registrácii

### **AccuWeather**
- 🆓 **50 volaní/deň**
- 📍 **Registrácia:** https://developer.accuweather.com/
- 🔑 **API kľúč:** Zadarmo po registrácii

### **Visual Crossing**
- 🆓 **1,000 volaní/deň**
- 📍 **Registrácia:** https://www.visualcrossing.com/weather-api
- 🔑 **API kľúč:** Zadarmo po registrácii

## 🚀 Spustenie s real weather

```bash
# 1. Nastavte API kľúč
export OPENWEATHER_API_KEY="váš_api_kľúč"

# 2. Spustite aplikáciu
python main.py

# 3. Použite hlasové príkazy:
# "aké je počasie"
# "teplota vonku"
# "počasie v Košiciach"
# "predpoveď počasia"
```

## 🎯 Podporované príkazy

### **Aktuálne počasie:**
- "aké je počasie"
- "počasie dnes"
- "aktuálne počasie"

### **Teplota:**
- "teplota vonku"
- "koľko stupňov je"
- "aká je teplota"

### **Predpoveď:**
- "predpoveď počasia"
- "aké bude počasie"
- "počasie zajtra"

### **Konkrétne mesto:**
- "počasie v Košiciach"
- "ako je počasie v Prahe"
- "teplota v Bratislave"

### **Detaily:**
- "vlhkosť vzduchu"
- "rýchlosť vetra"
- "tlak vzduchu"

## ⚠️ Riešenie problémov

### **Chyba: "Invalid API key"**
```
✅ Skontrolujte API kľúč
✅ Počkajte 10-60 minút po registrácii
✅ Skontrolujte systémové premenné
```

### **Chyba: "City not found"**
```
✅ Skontrolujte názov mesta
✅ Použite anglické názvy
✅ Skúste "Bratislava,SK"
```

### **Chyba: "Request timeout"**
```
✅ Skontrolujte internetové pripojenie
✅ Skúste neskôr
✅ Použije sa demo režim
```

## 🎉 Hotovo!

Po nastavení API kľúča budete mať prístup k aktuálnym weather dátam z celého sveta! 🌍
