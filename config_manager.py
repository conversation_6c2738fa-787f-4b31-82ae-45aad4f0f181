
import json
import os
from pathlib import Path

class ConfigManager:
    def __init__(self, config_file='config.json'):
        self.config_file = Path(config_file)
        self._load_env_file()  # Načítanie .env súboru ak existuje
        self.config = self.load_config()

    def load_config(self):
        if not self.config_file.exists():
            raise FileNotFoundError(f"Configuration file {self.config_file} not found.")

        with open(self.config_file, 'r', encoding='utf-8') as file:
            return json.load(file)

    def get(self, *keys, default=None):
        value = self.config
        for key in keys:
            if isinstance(value, dict):  # Ko<PERSON>rola, či je hodnota slovník
                value = value.get(key)
                if value is None:
                    return default
            else:
                return default  # Ak nie je slovník, vr<PERSON>ti predvolenú hodnotu
        return value

    def _load_env_file(self):
        """Načíta .env súbor ak existuje a nastaví environment variables."""
        env_file = Path('.env')
        if env_file.exists():
            try:
                with open(env_file, 'r', encoding='utf-8') as file:
                    for line in file:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip().strip('"').strip("'")
                            if key and not os.environ.get(key):
                                os.environ[key] = value
            except Exception as e:
                print(f"Upozornenie: Chyba pri načítaní .env súboru: {e}")