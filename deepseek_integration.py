import os
import re
import json
import logging
from typing import Dict, List, Optional
import google.generativeai as genai
from config_manager import ConfigManager
from datetime import datetime
import time

logging.basicConfig(level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class GeminiAPI:
    """
    Vylepšený API klient pre Gemini model s pokročilými funkciami.

    Funkcie:
    - Inteligentné r<PERSON> otázok
    - Kontextové odpovede
    - Pamäť konverzácie
    - Rôzne typy promptov
    - Optimalizované pre slovenčinu
    """

    def __init__(self, config_manager=None):
        # Načítanie API kľúča zo systémových premenných
        self.api_key = os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("Environment variable GEMINI_API_KEY is not set.")

        # Nač<PERSON>tanie konfigurácie
        self.config = config_manager or ConfigManager()
        gemini_config = self.config.get("gemini", default={})

        # Konfigurácia Gemini API
        genai.configure(api_key=self.api_key)
        model_name = gemini_config.get("model", "gemini-2.0-flash")
        self.model = genai.GenerativeModel(model_name)
        self.timeout = gemini_config.get("timeout", 30)

        # Nové nastavenia pre vylepšené odpovede
        self.max_response_length = gemini_config.get("max_response_length", 300)
        self.context_memory = gemini_config.get("context_memory", True)
        self.conversation_history = []
        self.max_history_length = gemini_config.get("max_history_length", 5)

        # Rozšírené rozpoznávanie otázok
        self.question_patterns = self._load_question_patterns()

        logger.info(f"GeminiAPI initialized with model: {model_name}")
        logger.info(f"Context memory: {self.context_memory}, Max response: {self.max_response_length}")

    def _load_question_patterns(self) -> List[str]:
        """Načíta vzory pre rozpoznávanie otázok z konfigurácie."""
        ask_commands = self.config.get("voice", "commands", "ask", default=[])

        # Základné vzory
        base_patterns = [
            r"^(ako|čo|prečo|kedy|kto|kde|aký|aká|aké|akí|koľko|môžem|má)\b",
            r"\b(je|sú|bude|budú|bol|bola|boli|môže|môžu)\b.*\?",
            r"\b(vysvetli|povedz|definuj|popíš|objasni)\b",
            r"\b(čo znamená|čo je|ako funguje|prečo sa)\b",
            r"\?$"  # Končí otáznikom
        ]

        # Pridanie vzory z konfigurácie
        config_patterns = [rf"\b{re.escape(cmd)}\b" for cmd in ask_commands]

        return base_patterns + config_patterns

    def _is_question(self, command: str) -> bool:
        """Rozšírené rozpoznávanie otázok."""
        command_lower = command.lower().strip()

        # Kontrola všetkých vzorov
        for pattern in self.question_patterns:
            if re.search(pattern, command_lower):
                return True

        return False

    def _add_to_history(self, user_input: str, ai_response: str):
        """Pridá konverzáciu do histórie."""
        if not self.context_memory:
            return

        self.conversation_history.append({
            "user": user_input,
            "ai": ai_response,
            "timestamp": datetime.now().isoformat()
        })

        # Udržiavanie maximálnej dĺžky histórie
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history.pop(0)

    def _get_context_prompt(self) -> str:
        """Vytvorí kontextový prompt z histórie konverzácie."""
        if not self.context_memory or not self.conversation_history:
            return ""

        context = "\n[KONTEXT PREDCHÁDZAJÚCEJ KONVERZÁCIE]\n"
        for entry in self.conversation_history[-3:]:  # Posledné 3 výmeny
            context += f"Používateľ: {entry['user']}\n"
            context += f"Asistent: {entry['ai']}\n"
        context += "[KONIEC KONTEXTU]\n\n"

        return context

    def process_command(self, command: str) -> Dict[str, str]:
        """
        Vylepšené spracovanie príkazov s kontextom a lepšími odpoveďami.

        Args:
            command (str): Hlasový príkaz od používateľa

        Returns:
            Dict[str, str]: Odpoveď s kľúčmi "message", "status", prípadne "error"
        """
        command = command.strip()
        is_question = self._is_question(command)

        logger.info(f"Processing command: '{command}' (is_question: {is_question})")

        try:
            if is_question:
                return self._process_question(command)
            else:
                return self._process_action_command(command)

        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            error_message = self._get_friendly_error_message(str(e))
            return {"message": error_message, "status": "error", "error": str(e)}

    def _process_question(self, question: str) -> Dict[str, str]:
        """Spracuje otázku a vráti informatívnu odpoveď."""
        context_prompt = self._get_context_prompt()
        prompt = self._build_question_prompt(question, context_prompt)

        try:
            response = self.model.generate_content(prompt)
            response_text = response.text.strip()

            # Vyčistenie a formátovanie odpovede
            clean_response = self._clean_and_format_response(response_text)

            # Pridanie do histórie
            self._add_to_history(question, clean_response)

            logger.debug(f"Question response: {clean_response}")
            return {"message": clean_response, "status": "success"}

        except Exception as e:
            logger.error(f"Error processing question: {e}")
            return {"message": "Prepáčte, momentálne neviem odpovedať na túto otázku.", "status": "error"}

    def _process_action_command(self, command: str) -> Dict[str, str]:
        """Spracuje akčný príkaz a vráti JSON odpoveď."""
        context_prompt = self._get_context_prompt()
        prompt = self._build_action_prompt(command, context_prompt)

        try:
            response = self.model.generate_content(prompt)
            response_text = response.text.strip()
            logger.debug(f"Raw action response: {response_text}")

            # Extrakcia JSON bloku
            json_data = self._extract_json_response(response_text)

            if json_data and "intent" in json_data and "action_command" in json_data:
                # Pridanie do histórie
                self._add_to_history(command, json_data.get("intent", "Akcia vykonaná"))
                return {"message": json_data, "status": "success"}
            else:
                raise ValueError("Invalid JSON response: missing required keys")

        except Exception as e:
            logger.error(f"Error processing action command: {e}")
            return {"message": "Nerozumiem tomuto príkazu.", "status": "error"}

    def _clean_and_format_response(self, response: str) -> str:
        """Vyčistí a naformátuje odpoveď pre lepšiu čitateľnosť."""
        # Odstránenie prebytočných znakov
        response = re.sub(r'\*+', '', response)  # Odstránenie hviezd
        response = re.sub(r'\#+', '', response)  # Odstránenie mriežok
        response = re.sub(r'\s+', ' ', response)  # Normalizácia medzier
        response = response.strip()

        # Skrátenie ak je príliš dlhé
        if len(response) > self.max_response_length:
            # Nájdenie poslednej vety v limite
            sentences = re.split(r'[.!?]+', response)
            result = ""
            for sentence in sentences:
                if len(result + sentence) <= self.max_response_length - 10:
                    result += sentence + ". "
                else:
                    break
            response = result.strip()

        return response

    def _extract_json_response(self, response_text: str) -> Optional[Dict]:
        """Extrahuje JSON z odpovede s lepším error handling."""
        # Pokus o nájdenie JSON bloku - jednoduchšie vzory
        json_patterns = [
            r'\{[^{}]*\{[^{}]*\}[^{}]*\}',  # Vnorené JSON
            r'\{[^{}]*\}',  # Jednoduchý JSON
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, response_text, re.DOTALL)
            for match in matches:
                try:
                    parsed = json.loads(match)
                    # Overenie, že obsahuje požadované kľúče
                    if isinstance(parsed, dict) and "intent" in parsed and "action_command" in parsed:
                        return parsed
                except json.JSONDecodeError:
                    continue

        # Pokus o nájdenie JSON pomocou jednoduchšieho prístupu
        try:
            # Nájdenie prvej { a poslednej }
            start = response_text.find('{')
            end = response_text.rfind('}')
            if start != -1 and end != -1 and end > start:
                json_str = response_text[start:end+1]
                parsed = json.loads(json_str)
                if isinstance(parsed, dict) and "intent" in parsed and "action_command" in parsed:
                    return parsed
        except json.JSONDecodeError:
            pass

        return None

    def _get_friendly_error_message(self, error: str) -> str:
        """Vráti používateľsky prívetivú chybovú správu."""
        if "quota" in error.lower() or "limit" in error.lower():
            return "Momentálne je AI asistent preťažený. Skúste to o chvíľu."
        elif "network" in error.lower() or "connection" in error.lower():
            return "Problém s internetovým pripojením. Skontrolujte pripojenie."
        elif "api" in error.lower():
            return "Problém s AI službou. Skúste to neskôr."
        else:
            return "Nastala neočakávaná chyba. Skúste to znovu."

    def _build_question_prompt(self, question: str, context: str = "") -> str:
        """Vytvorí optimalizovaný prompt pre otázky."""
        prompt = f"""{context}
[INŠTRUKCIE PRE AI ASISTENTA - REŽIM OTÁZOK]

Si inteligentný AI asistent, ktorý odpovedá na otázky v slovenčine. Tvoja úloha je:

1. **Poskytovať presné a užitočné odpovede** na otázky používateľa
2. **Odpovedať v slovenčine** prirodzeným a zrozumiteľným spôsobom
3. **Byť stručný ale informatívny** - ideálne 1-3 vety
4. **Zamerať sa na podstatu** otázky a poskytnúť praktické informácie
5. **Ak nevieš odpoveď**, úprimne to priznej a navrhni alternatívu

**Špeciálne pravidlá:**
- Pre technické otázky: Vysvetli jednoducho, bez zbytočného žargónu
- Pre všeobecné otázky: Buď faktický a objektívny
- Pre definície: Poskytni jasnú a stručnú definíciu s príkladom ak je to užitočné
- Nepoužívaj markdown formátovanie (*, #, atď.)

**Otázka používateľa:** {question}

**Odpoveď:**"""

        return prompt

    def _build_action_prompt(self, command: str, context: str = "") -> str:
        """Vytvorí optimalizovaný prompt pre akčné príkazy."""
        prompt = f"""{context}
[INŠTRUKCIE PRE AI ASISTENTA - REŽIM AKCIÍ]

Si AI asistent pre ovládanie počítača hlasovými príkazmi v slovenčine.

**DOSTUPNÉ AKCIE (metódy triedy Cursor):**
- click() - ľavé kliknutie myši
- right_click() - pravé kliknutie myši
- double_click() - dvojklik
- scroll_up() - posun hore
- scroll_down() - posun dole
- volume_set(value) - nastavenie hlasitosti (0-100)
- volume_up() - zvýšenie hlasitosti
- volume_down() - zníženie hlasitosti
- open_webpage(url) - otvorenie webovej stránky
- open_application(app_name) - otvorenie aplikácie
- save_file() - uloženie súboru (Ctrl+S)
- copy() - kopírovanie (Ctrl+C)
- paste() - vloženie (Ctrl+V)
- undo() - vrátenie späť (Ctrl+Z)
- close_window() - zatvorenie okna
- switch_application() - prepnutie aplikácie

**FORMÁT ODPOVEDE:**
Vráť odpoveď VÝHRADNE v JSON formáte:
{{
    "intent": "stručný popis zámeru v slovenčine",
    "action_command": {{
        "name": "názov_metódy",
        "parameters": {{}} alebo {{"parameter": "hodnota"}}
    }}
}}

**PRÍKLADY:**
- "klikni" → {{"intent": "kliknutie myši", "action_command": {{"name": "click", "parameters": {{}}}}}}
- "hlasitosť na 50" → {{"intent": "nastavenie hlasitosti", "action_command": {{"name": "volume_set", "parameters": {{"value": 50}}}}}}
- "otvor google" → {{"intent": "otvorenie Google", "action_command": {{"name": "open_webpage", "parameters": {{"url": "https://www.google.com"}}}}}}

**Príkaz používateľa:** {command}

**JSON odpoveď:**"""

        return prompt

    def clear_conversation_history(self):
        """Vymaže históriu konverzácie."""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")

    def get_conversation_summary(self) -> Dict:
        """Vráti súhrn konverzácie."""
        return {
            "total_exchanges": len(self.conversation_history),
            "context_memory_enabled": self.context_memory,
            "max_history_length": self.max_history_length,
            "last_interaction": self.conversation_history[-1]["timestamp"] if self.conversation_history else None
        }

    def set_response_length(self, length: int):
        """Nastaví maximálnu dĺžku odpovede."""
        if 50 <= length <= 1000:
            self.max_response_length = length
            logger.info(f"Response length set to {length}")
        else:
            logger.warning(f"Invalid response length: {length}. Must be between 50-1000")

    def toggle_context_memory(self, enabled: bool = None):
        """Zapne/vypne pamäť kontextu."""
        if enabled is None:
            self.context_memory = not self.context_memory
        else:
            self.context_memory = enabled

        logger.info(f"Context memory {'enabled' if self.context_memory else 'disabled'}")

        if not self.context_memory:
            self.clear_conversation_history()

if __name__ == "__main__":
    # Pre testovanie - uistite sa, že máte nastavený GEMINI_API_KEY v systémových premenných
    try:
        print("🧪 Testovanie vylepšenej Gemini integrácie...")
        gemini = GeminiAPI()

        # Test otázok
        print("\n📋 Test otázok:")
        questions = [
            "Čo je Python?",
            "Ako funguje umelá inteligencia?",
            "Prečo je dôležité programovanie?",
            "Definuj machine learning"
        ]

        for question in questions:
            print(f"\n❓ Otázka: {question}")
            response = gemini.process_command(question)
            print(f"✅ Odpoveď: {response['message']}")
            print(f"📊 Status: {response['status']}")

        # Test akčných príkazov
        print("\n🎮 Test akčných príkazov:")
        commands = [
            "klikni",
            "hlasitosť na 75",
            "otvor youtube",
            "ulož súbor"
        ]

        for command in commands:
            print(f"\n🎯 Príkaz: {command}")
            response = gemini.process_command(command)
            print(f"✅ Odpoveď: {response['message']}")
            print(f"📊 Status: {response['status']}")

        # Test histórie konverzácie
        print(f"\n📚 Súhrn konverzácie:")
        summary = gemini.get_conversation_summary()
        for key, value in summary.items():
            print(f"  {key}: {value}")

    except ValueError as e:
        print(f"❌ Chyba: {e}")
        print("💡 Nastavte GEMINI_API_KEY v systémových premenných.")
    except Exception as e:
        print(f"❌ Neočakávaná chyba: {e}")
