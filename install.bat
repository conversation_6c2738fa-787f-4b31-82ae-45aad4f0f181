@echo off
REM AirCursor Assistant - Windows Installation Script
REM Automatická inš<PERSON>cia pre Windows používateľov

setlocal enabledelayedexpansion

echo.
echo ========================================
echo   AirCursor Assistant - Windows Setup
echo ========================================
echo.

REM Kontrola Python
echo [INFO] Kontrolujem Python 3.12...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python nie je nainštalovaný alebo nie je v PATH
    echo.
    echo Prosím nainštalujte Python 3.12+ z:
    echo https://www.python.org/downloads/
    echo.
    echo Alebo použite chocolatey:
    echo choco install python312
    echo.
    echo Alebo použite winget:
    echo winget install Python.Python.3.12
    pause
    exit /b 1
)

python --version
echo [SUCCESS] Python je dostupný

REM Spustenie Python inštalačného scriptu
echo.
echo [INFO] Spúšťam Python inštalačný script...
python install.py

if errorlevel 1 (
    echo [ERROR] Inštalácia zlyhala
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Inštalácia dokončená!
echo.
echo Ďalšie kroky:
echo 1. Nastavte GEMINI_API_KEY environment variable
echo 2. Spustite aplikáciu pomocou: run.bat
echo.
pause
