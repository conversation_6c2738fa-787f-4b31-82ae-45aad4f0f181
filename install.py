#!/usr/bin/env python3
"""
AirCursor Assistant - <PERSON><PERSON><PERSON> script

Tento script automaticky:
1. Kontroluje Python verziu
2. Vytvára virtual environment
3. Inštaluje závislosti
4. Nastavuje environment variables
5. Testuje základnú funkcionalitu

Použitie:
    python install.py
    python install.py --dev  # Pre development inštaláciu
"""

import sys
import subprocess
import platform
import venv
from pathlib import Path
import argparse

class Colors:
    """ANSI color codes pre farebný výstup."""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

class AirCursorInstaller:
    """Hlavná trieda pre inštaláciu AirCursor Assistant."""

    def __init__(self, dev_mode=False):
        self.dev_mode = dev_mode
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        self.python_executable = sys.executable
        self.min_python_version = (3, 12)

    def print_status(self, message, status="INFO"):
        """Vytlačí farebný status message."""
        colors = {
            "INFO": Colors.BLUE,
            "SUCCESS": Colors.GREEN,
            "WARNING": Colors.YELLOW,
            "ERROR": Colors.RED
        }
        color = colors.get(status, Colors.BLUE)
        print(f"{color}[{status}]{Colors.END} {message}")

    def check_python_version(self):
        """Kontrola Python verzie."""
        self.print_status("Kontrolujem Python verziu...")
        current_version = sys.version_info[:2]

        if current_version < self.min_python_version:
            self.print_status(
                f"Python {self.min_python_version[0]}.{self.min_python_version[1]}+ je potrebný. "
                f"Máte {current_version[0]}.{current_version[1]}",
                "ERROR"
            )
            return False

        self.print_status(
            f"Python {current_version[0]}.{current_version[1]} ✓",
            "SUCCESS"
        )
        return True

    def create_virtual_environment(self):
        """Vytvorí virtual environment."""
        if self.venv_path.exists():
            self.print_status("Virtual environment už existuje", "WARNING")
            return True

        self.print_status("Vytváram virtual environment...")
        try:
            venv.create(self.venv_path, with_pip=True)
            self.print_status("Virtual environment vytvorený ✓", "SUCCESS")
            return True
        except Exception as e:
            self.print_status(f"Chyba pri vytváraní venv: {e}", "ERROR")
            return False

    def get_venv_python(self):
        """Získa cestu k Python executable vo venv."""
        if platform.system() == "Windows":
            return self.venv_path / "Scripts" / "python.exe"
        else:
            return self.venv_path / "bin" / "python"

    def get_venv_pip(self):
        """Získa cestu k pip executable vo venv."""
        if platform.system() == "Windows":
            return self.venv_path / "Scripts" / "pip.exe"
        else:
            return self.venv_path / "bin" / "pip"

    def upgrade_pip(self):
        """Aktualizuje pip vo virtual environment."""
        self.print_status("Aktualizujem pip...")
        pip_path = self.get_venv_pip()

        try:
            subprocess.run([
                str(pip_path), "install", "--upgrade", "pip"
            ], check=True, capture_output=True)
            self.print_status("Pip aktualizovaný ✓", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            self.print_status(f"Chyba pri aktualizácii pip: {e}", "ERROR")
            return False

    def install_dependencies(self):
        """Inštaluje závislosti z requirements.txt."""
        requirements_file = "requirements.txt"
        if self.dev_mode:
            # Pre development môžeme pridať extra závislosti
            requirements_file = "requirements.txt"

        if not Path(requirements_file).exists():
            self.print_status(f"{requirements_file} neexistuje", "ERROR")
            return False

        self.print_status(f"Inštalujem závislosti z {requirements_file}...")
        pip_path = self.get_venv_pip()

        try:
            subprocess.run([
                str(pip_path), "install", "-r", requirements_file
            ], check=True)
            self.print_status("Závislosti nainštalované ✓", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            self.print_status(f"Chyba pri inštalácii závislostí: {e}", "ERROR")
            return False

    def setup_environment_variables(self):
        """Nastavuje environment variables."""
        self.print_status("Nastavujem environment variables...")

        # Spustíme setup_env.py script
        python_path = self.get_venv_python()
        try:
            subprocess.run([
                str(python_path), "setup_env.py"
            ], check=True)
            self.print_status("Environment variables nastavené ✓", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            self.print_status(f"Chyba pri nastavovaní env variables: {e}", "WARNING")
            return True  # Nie je kritické

    def test_installation(self):
        """Testuje základnú funkcionalitu."""
        self.print_status("Testujem inštaláciu...")
        python_path = self.get_venv_python()

        # Test importov
        test_script = '''
import sys
try:
    import cv2
    import mediapipe
    import pyautogui
    import speech_recognition
    import gtts
    print("SUCCESS: Všetky základné moduly sa importovali úspešne")
except ImportError as e:
    print(f"ERROR: Chyba pri importe: {e}")
    sys.exit(1)
'''

        try:
            result = subprocess.run([
                str(python_path), "-c", test_script
            ], capture_output=True, text=True, check=True)

            if "SUCCESS" in result.stdout:
                self.print_status("Test inštalácie prešiel ✓", "SUCCESS")
                return True
            else:
                self.print_status("Test inštalácie zlyhal", "ERROR")
                return False

        except subprocess.CalledProcessError as e:
            self.print_status(f"Test inštalácie zlyhal: {e}", "ERROR")
            return False

    def create_activation_scripts(self):
        """Vytvorí skripty pre aktiváciu virtual environment."""
        self.print_status("Vytváram aktivačné skripty...")

        # Windows batch script
        if platform.system() == "Windows":
            activate_script = self.project_root / "activate.bat"
            with open(activate_script, 'w') as f:
                f.write(f'''@echo off
echo Aktivujem AirCursor Assistant virtual environment...
call "{self.venv_path}\\Scripts\\activate.bat"
echo Virtual environment aktivovaný!
echo Pre spustenie aplikácie použite: python main.py
cmd /k
''')

        # Unix shell script
        activate_script = self.project_root / "activate.sh"
        with open(activate_script, 'w') as f:
            f.write(f'''#!/bin/bash
echo "Aktivujem AirCursor Assistant virtual environment..."
source "{self.venv_path}/bin/activate"
echo "Virtual environment aktivovaný!"
echo "Pre spustenie aplikácie použite: python main.py"
bash
''')

        # Spustiteľný script
        run_script = self.project_root / "run.py"
        with open(run_script, 'w') as f:
            f.write(f'''#!/usr/bin/env python3
"""
Spustiteľný script pre AirCursor Assistant
Automaticky aktivuje virtual environment a spustí aplikáciu.
"""
import subprocess
import sys
from pathlib import Path

venv_python = Path(__file__).parent / "venv" / {"'Scripts' / 'python.exe'" if platform.system() == "Windows" else "'bin' / 'python'"}

if not venv_python.exists():
    print("Virtual environment neexistuje. Spustite najprv: python install.py")
    sys.exit(1)

subprocess.run([str(venv_python), "main.py"])
''')

        self.print_status("Aktivačné skripty vytvorené ✓", "SUCCESS")

    def print_final_instructions(self):
        """Vytlačí finálne inštrukcie."""
        print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 Inštalácia dokončená!{Colors.END}\n")

        print(f"{Colors.BLUE}📋 Ďalšie kroky:{Colors.END}")
        print("1. Nastavte GEMINI_API_KEY environment variable")
        print("   - Získajte API kľúč: https://makersuite.google.com/app/apikey")
        print("   - Vyplňte ho v .env súbore")

        print(f"\n{Colors.BLUE}🚀 Spustenie aplikácie:{Colors.END}")
        if platform.system() == "Windows":
            print("   activate.bat          # Aktivácia virtual environment")
            print("   python run.py         # Priame spustenie")
        else:
            print("   source activate.sh    # Aktivácia virtual environment")
            print("   python run.py         # Priame spustenie")

        print(f"\n{Colors.BLUE}📖 Dokumentácia:{Colors.END}")
        print("   README.md            # Hlavná dokumentácia")
        print("   USER_GUIDE.md        # Užívateľská príručka")
        print("   API_DOCS.md          # Vývojárska dokumentácia")

    def install(self):
        """Hlavná inštalačná metóda."""
        print(f"{Colors.BOLD}🚀 AirCursor Assistant - Automatická inštalácia{Colors.END}")
        print("=" * 50)

        steps = [
            ("Kontrola Python verzie", self.check_python_version),
            ("Vytvorenie virtual environment", self.create_virtual_environment),
            ("Aktualizácia pip", self.upgrade_pip),
            ("Inštalácia závislostí", self.install_dependencies),
            ("Nastavenie environment", self.setup_environment_variables),
            ("Test inštalácie", self.test_installation),
            ("Vytvorenie aktivačných skriptov", self.create_activation_scripts),
        ]

        for step_name, step_func in steps:
            if not step_func():
                self.print_status(f"Inštalácia zlyhala na kroku: {step_name}", "ERROR")
                return False

        self.print_final_instructions()
        return True

def main():
    """Hlavná funkcia."""
    parser = argparse.ArgumentParser(description="AirCursor Assistant Installer")
    parser.add_argument("--dev", action="store_true", help="Development inštalácia")
    args = parser.parse_args()

    installer = AirCursorInstaller(dev_mode=args.dev)
    success = installer.install()

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
