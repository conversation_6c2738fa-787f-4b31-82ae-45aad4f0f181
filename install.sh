#!/bin/bash
# AirCursor Assistant - Unix Installation Script
# Automatick<PERSON> inštal<PERSON>cia pre Linux/macOS používateľov

set -e  # Exit on error

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo
echo "========================================"
echo "  AirCursor Assistant - Unix Setup"
echo "========================================"
echo

# Check Python
print_status "Kontrolujem Python 3.12..."
if ! command -v python3.12 &> /dev/null && ! command -v python3 &> /dev/null; then
    print_error "Python 3.12 nie je nainštalovaný"
    echo
    echo "Inštalácia Python 3.12:"
    echo "Ubuntu/Debian: sudo apt-get install python3.12 python3.12-pip python3.12-venv"
    echo "CentOS/RHEL:   sudo yum install python3.12 python3.12-pip"
    echo "Arch Linux:    sudo pacman -S python312 python-pip"
    echo "macOS:         brew install python@3.12"
    exit 1
fi

# Use python3.12 if available, otherwise python3
if command -v python3.12 &> /dev/null; then
    PYTHON_CMD="python3.12"
else
    PYTHON_CMD="python3"
fi

$PYTHON_CMD --version
print_success "Python je dostupný"

# Check pip
print_status "Kontrolujem pip..."
if command -v python3.12 &> /dev/null; then
    PIP_CMD="python3.12 -m pip"
elif command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
else
    print_warning "pip nie je dostupný, pokúšam sa nainštalovať..."

    # Try to install pip
    if command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y python3.12-pip
    elif command -v yum &> /dev/null; then
        sudo yum install -y python3.12-pip
    elif command -v pacman &> /dev/null; then
        sudo pacman -S python-pip
    elif command -v brew &> /dev/null; then
        # pip should be included with Python from brew
        print_warning "pip by mal byť súčasťou Python z homebrew"
    else
        print_error "Nepodarilo sa nainštalovať pip automaticky"
        exit 1
    fi
    PIP_CMD="python3.12 -m pip"
fi

print_success "pip je dostupný"

# Make script executable
chmod +x install.py
chmod +x setup_env.py
chmod +x package_manager.py

# Run Python installation script
echo
print_status "Spúšťam Python inštalačný script..."
$PYTHON_CMD install.py

if [ $? -ne 0 ]; then
    print_error "Inštalácia zlyhala"
    exit 1
fi

# Make other scripts executable
chmod +x run.sh
chmod +x setup.sh

echo
print_success "Inštalácia dokončená!"
echo
echo "Ďalšie kroky:"
echo "1. Nastavte GEMINI_API_KEY environment variable"
echo "2. Spustite aplikáciu pomocou: ./run.sh"
echo "3. Alebo použite make: make run"
echo
