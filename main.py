import tkinter as tk
import threading
import cv2
from queue import Queue
from air_cursor import AirCursor
from voice_commands import Cursor, voice_command_listener
from config_manager import ConfigManager
from gtts import gTTS
from playsound3 import playsound
import os
from PIL import Image, ImageTk
import logging

# Import nových modulov (Fáza 5)
from ui_animations import AnimationManager, animate_widget
from custom_voice_commands import CustomVoiceCommandManager
from plugin_system import PluginManager
try:
    from web_interface import WebInterface
    WEB_INTERFACE_AVAILABLE = True
except ImportError:
    WEB_INTERFACE_AVAILABLE = False

# Import weather integrácie
try:
    from weather_integration import process_weather_command
    WEATHER_AVAILABLE = True
except ImportError:
    WEATHER_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def speak(text: str, lang: str = "sk", headphones_enabled: bool = True):
    """Prehrá text ako reč, ak sú slúchadlá povolené."""
    if not headphones_enabled:
        return
    try:
        tts = gTTS(text=text, lang=lang)
        filename = "temp_audio.mp3"
        tts.save(filename)
        playsound(filename)
        os.remove(filename)
    except Exception as e:
        logger.error(f"Chyba pri prehrávaní TTS: {e}")

def handle_weather_command(command):
    """Spracuje weather príkaz."""
    weather_responses = {
        "aké je počasie": "V Bratislave je momentálne 20°C, jasno. Vlhkosť 65%. (Demo údaje)",
        "počasie dnes": "V Bratislave je momentálne 20°C, jasno. Vlhkosť 65%. (Demo údaje)",
        "teplota vonku": "Aktuálna teplota je 20°C, pocitovo 22°C. (Demo údaje)",
        "aké bude počasie": "Predpoveď na najbližšie dni: Dnes slnečno 22°C, zajtra oblačno 18°C, pozajtra dážď 15°C. (Demo údaje)",
        "predpoveď počasia": "Predpoveď na najbližšie dni: Dnes slnečno 22°C, zajtra oblačno 18°C, pozajtra dážď 15°C. (Demo údaje)"
    }

    command_lower = command.lower().strip()

    # Presná zhoda
    if command_lower in weather_responses:
        return True, weather_responses[command_lower]

    # Čiastočná zhoda
    for trigger, response in weather_responses.items():
        if any(word in command_lower for word in trigger.split()):
            if "počasie v" in command_lower:
                city = "Bratislava"  # Default
                if " v " in command_lower:
                    parts = command_lower.split(" v ")
                    if len(parts) > 1:
                        city = parts[1].strip().replace("?", "").replace(".", "").title()
                return True, f"V meste {city} je 20°C, jasno. Vlhkosť 65%. (Demo údaje)"
            return True, response

    return False, ""

def enhanced_voice_command_listener(cursor: Cursor, queue: Queue):
    """Vylepšený voice command listener s weather podporou."""
    import speech_recognition as sr

    recognizer = sr.Recognizer()
    microphone = sr.Microphone()

    logger.info("Enhanced voice command listener spustený s weather podporou.")

    while cursor.running:
        if not cursor.mic_enabled:
            continue

        try:
            with microphone as source:
                recognizer.adjust_for_ambient_noise(source, duration=0.5)
                audio = recognizer.listen(source, timeout=5)

            command = recognizer.recognize_google(audio, language="sk-SK").lower()
            queue.put(("stt", command))
            logger.info(f"Rozpoznaný príkaz: '{command}'")

            # 1. Weather príkazy (najvyššia priorita)
            weather_handled, weather_response = handle_weather_command(command)
            if weather_handled:
                queue.put(("tts", weather_response))
                continue

            # 2. Základné príkazy kurzora
            handled = False
            if hasattr(cursor, 'command_map'):
                for cmd_key, cmd_list in cursor.commands.items():
                    if any(cmd in command for cmd in cmd_list):
                        if cmd_key in cursor.command_map:
                            func, response = cursor.command_map[cmd_key]
                            func()
                            queue.put(("tts", response))
                            handled = True
                            break

            if handled:
                continue

            # 3. Gemini API ako fallback
            if cursor.gemini:
                try:
                    is_question = any(word in command for word in ["aké", "ako", "kedy", "kde", "prečo", "čo", "koľko"])
                    logger.info(f"Processing command: '{command}' (is_question: {is_question})")

                    if is_question:
                        response = cursor.gemini.ask_question(command)
                        queue.put(("tts", response))
                    else:
                        response = cursor.gemini.process_command(command)
                        if isinstance(response, dict) and response.get("status") == "success":
                            queue.put(("tts", response.get("message", "Príkaz vykonaný.")))
                        else:
                            queue.put(("tts", str(response)))
                except Exception as e:
                    logger.error(f"Chyba pri volaní Gemini: {e}")
                    queue.put(("tts", "Chyba pri spracovaní príkazu."))
            else:
                queue.put(("tts", "Príkaz nebol rozpoznaný."))

        except sr.UnknownValueError:
            pass  # Ignorovať nerozpoznané príkazy
        except sr.WaitTimeoutError:
            continue
        except Exception as e:
            logger.error(f"Chyba pri spracovaní hlasu: {e}")
            queue.put(("tts", "Nastala chyba."))

def run_cursor_tracking(air_cursor: AirCursor, cap: cv2.VideoCapture, queue: Queue):
    """Spustí sledovanie kurzora."""
    while air_cursor.running:
        ret, frame = cap.read()
        if not ret:
            break

        frame = cv2.flip(frame, 1)
        finger_pos = air_cursor.process_frame(frame)

        if finger_pos and air_cursor.calibrated:
            air_cursor.update_cursor(finger_pos)

        if not air_cursor.calibrated:
            if finger_pos:
                cv2.putText(frame, f"Kalibrácia {len(air_cursor.calibration_sets) + 1}/3 - Bod {len(air_cursor.corners) + 1}/4",
                            (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        for i, corner in enumerate(air_cursor.corners):
            cv2.circle(frame, corner, 5, (0, 0, 255), -1)
            cv2.putText(frame, str(i + 1), (corner[0] - 10, corner[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            if i > 0:
                cv2.line(frame, air_cursor.corners[i - 1], corner, (0, 255, 255), 1)
        if len(air_cursor.corners) >= 4:
            cv2.line(frame, air_cursor.corners[-1], air_cursor.corners[0], (0, 255, 255), 1)

        cv2.imshow("AirCursor", frame)
        key = cv2.waitKey(1) & 0xFF
        if key == ord("a"):
            air_cursor.auto_calibrate(frame)
        elif key == ord("r"):
            air_cursor.corners = []
            air_cursor.calibration_sets = []
            air_cursor.calibrated = False
            air_cursor.prev_cursor = None
            queue.put(("tts", "Kalibrácia zresetovaná"))

class ModernGUI:
    def __init__(self, root: tk.Tk, queue: Queue, cursor: Cursor, air_cursor: AirCursor):
        self.root = root
        self.queue = queue
        self.cursor = cursor
        self.air_cursor = air_cursor
        self.root.title("AirCursor Assistant v1.3.0 - Fáza 5")
        self.root.geometry("1200x700")
        self.root.configure(bg="#2E3440")

        # Inicializácia nových manažérov (Fáza 5)
        self.animation_manager = AnimationManager()
        self.custom_commands_manager = CustomVoiceCommandManager()
        self.plugin_manager = PluginManager()
        self.web_interface = None

        # Inicializácia webového rozhrania
        if WEB_INTERFACE_AVAILABLE:
            try:
                self.web_interface = WebInterface(
                    config_manager=None,  # Bude pridané neskôr
                    plugin_manager=self.plugin_manager,
                    custom_commands_manager=self.custom_commands_manager,
                    port=8080
                )
            except Exception as e:
                logger.warning(f"Webové rozhranie sa nepodarilo inicializovať: {e}")

        # Objavenie a načítanie pluginov
        self.plugin_manager.discover_plugins()
        self._load_enabled_plugins()

        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=0)
        self.root.grid_rowconfigure(2, weight=0)
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)

        # GUI prvky (bez zmien vzhľadu)
        self.speech_frame = tk.Frame(self.root, bg="#3B4252", bd=2, relief=tk.SUNKEN)
        self.speech_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        tk.Label(self.speech_frame, text="Google Speech-to-Text", bg="#3B4252", fg="#D8DEE9", font=("Helvetica", 14, "bold")).pack(fill=tk.X, padx=10, pady=5)
        self.speech_text = tk.Text(self.speech_frame, wrap=tk.WORD, bg="#3B4252", fg="#D8DEE9", font=("Helvetica", 12))
        self.speech_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.tts_frame = tk.Frame(self.root, bg="#3B4252", bd=2, relief=tk.SUNKEN)
        self.tts_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)
        tk.Label(self.tts_frame, text="TTS Output", bg="#3B4252", fg="#D8DEE9", font=("Helvetica", 14, "bold")).pack(fill=tk.X, padx=10, pady=5)
        self.tts_text = tk.Text(self.tts_frame, wrap=tk.WORD, bg="#3B4252", fg="#D8DEE9", font=("Helvetica", 12))
        self.tts_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.icons_frame = tk.Frame(self.root, bg="#2E3440")
        self.icons_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        # Načítanie ikon s error handling
        try:
            self.mic_icon = ImageTk.PhotoImage(Image.open("microphone.png").resize((32, 32), Image.LANCZOS))
            tk.Label(self.icons_frame, image=self.mic_icon, bg="#2E3440").pack(side=tk.LEFT, padx=10, pady=5)
        except (FileNotFoundError, Exception) as e:
            logger.warning(f"Mikrofón ikona sa nenačítala: {e}")
            tk.Label(self.icons_frame, text="🎤", bg="#2E3440", fg="#D8DEE9", font=("Helvetica", 16)).pack(side=tk.LEFT, padx=10, pady=5)

        try:
            self.headphone_icon = ImageTk.PhotoImage(Image.open("headphones.png").resize((32, 32), Image.LANCZOS))
            tk.Label(self.icons_frame, image=self.headphone_icon, bg="#2E3440").pack(side=tk.LEFT, padx=10, pady=5)
        except (FileNotFoundError, Exception) as e:
            logger.warning(f"Slúchadlá ikona sa nenačítala: {e}")
            tk.Label(self.icons_frame, text="🎧", bg="#2E3440", fg="#D8DEE9", font=("Helvetica", 16)).pack(side=tk.LEFT, padx=10, pady=5)

        self.buttons_frame = tk.Frame(self.root, bg="#2E3440")
        self.buttons_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        # Pôvodné tlačidlá
        self.mic_button = tk.Button(self.buttons_frame, text="🎤 ON", command=self.toggle_mic, bg="green", fg="white", font=("Helvetica", 10))
        self.mic_button.pack(side=tk.LEFT, padx=5, pady=5)
        self.headphone_button = tk.Button(self.buttons_frame, text="🎧 ON", command=self.toggle_headphones, bg="green", fg="white", font=("Helvetica", 10))
        self.headphone_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Nové tlačidlá (Fáza 5)
        self.web_button = tk.Button(self.buttons_frame, text="🌐 Web", command=self.open_web_interface, bg="#4C566A", fg="white", font=("Helvetica", 10))
        self.web_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.plugins_button = tk.Button(self.buttons_frame, text="🔌 Pluginy", command=self.show_plugins, bg="#5E81AC", fg="white", font=("Helvetica", 10))
        self.plugins_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.commands_button = tk.Button(self.buttons_frame, text="🎤 Príkazy", command=self.show_custom_commands, bg="#88C0D0", fg="white", font=("Helvetica", 10))
        self.commands_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.animate_button = tk.Button(self.buttons_frame, text="✨ Animácie", command=self.test_animations, bg="#A3BE8C", fg="white", font=("Helvetica", 10))
        self.animate_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.mic_enabled = True
        self.headphones_enabled = True
        self.process_queue()

    def toggle_mic(self):
        """Prepnutie stavu mikrofónu."""
        self.mic_enabled = not self.mic_enabled
        self.cursor.mic_enabled = self.mic_enabled
        text = "🎤 ON" if self.mic_enabled else "🎤 OFF"
        self.mic_button.config(text=text, bg="green" if self.mic_enabled else "red")

        # Animácia tlačidla
        animated_button = animate_widget(self.mic_button)
        animated_button.pulse(0.5, 1)

    def toggle_headphones(self):
        """Prepnutie stavu slúchadiel."""
        self.headphones_enabled = not self.headphones_enabled
        self.cursor.headphones_enabled = self.headphones_enabled
        text = "🎧 ON" if self.headphones_enabled else "🎧 OFF"
        self.headphone_button.config(text=text, bg="green" if self.headphones_enabled else "red")

        # Animácia tlačidla
        animated_button = animate_widget(self.headphone_button)
        animated_button.pulse(0.5, 1)

    def open_web_interface(self):
        """Otvorí webové rozhranie."""
        if self.web_interface and WEB_INTERFACE_AVAILABLE:
            try:
                self.web_interface.start(open_browser=True)
                self.queue.put(("tts", "Webové rozhranie spustené na porte 8080"))

                # Animácia tlačidla
                animated_button = animate_widget(self.web_button)
                animated_button.pulse(1.0, 2)

            except Exception as e:
                logger.error(f"Chyba pri spúšťaní webového rozhrania: {e}")
                self.queue.put(("tts", "Chyba pri spúšťaní webového rozhrania"))
        else:
            self.queue.put(("tts", "Webové rozhranie nie je dostupné"))

    def show_plugins(self):
        """Zobrazí okno s pluginmi."""
        plugins_window = tk.Toplevel(self.root)
        plugins_window.title("🔌 Správa pluginov")
        plugins_window.geometry("600x400")
        plugins_window.configure(bg="#2E3440")

        # Animácia okna
        plugins_window.attributes('-alpha', 0.0)
        self._animate_window_fade_in(plugins_window)

        # Zoznam pluginov
        frame = tk.Frame(plugins_window, bg="#3B4252")
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        tk.Label(frame, text="Dostupné pluginy", bg="#3B4252", fg="#D8DEE9",
                font=("Helvetica", 14, "bold")).pack(pady=10)

        plugins = self.plugin_manager.list_plugins()
        if not plugins:
            tk.Label(frame, text="Žiadne pluginy nie sú nainštalované",
                    bg="#3B4252", fg="#D8DEE9").pack(pady=20)
        else:
            for plugin in plugins:
                plugin_frame = tk.Frame(frame, bg="#4C566A", relief=tk.RAISED, bd=1)
                plugin_frame.pack(fill=tk.X, padx=5, pady=5)

                info_text = f"{plugin.name} v{plugin.version} - {plugin.description}"
                tk.Label(plugin_frame, text=info_text, bg="#4C566A", fg="#D8DEE9",
                        font=("Helvetica", 10)).pack(side=tk.LEFT, padx=10, pady=5)

                status_text = "✅ Povolený" if plugin.enabled else "❌ Zakázaný"
                status_color = "#A3BE8C" if plugin.enabled else "#BF616A"
                tk.Label(plugin_frame, text=status_text, bg=status_color, fg="white",
                        font=("Helvetica", 9)).pack(side=tk.RIGHT, padx=10, pady=5)

    def show_custom_commands(self):
        """Zobrazí okno s vlastnými príkazmi."""
        commands_window = tk.Toplevel(self.root)
        commands_window.title("🎤 Vlastné hlasové príkazy")
        commands_window.geometry("700x500")
        commands_window.configure(bg="#2E3440")

        # Animácia okna
        commands_window.attributes('-alpha', 0.0)
        self._animate_window_fade_in(commands_window)

        # Hlavný frame
        main_frame = tk.Frame(commands_window, bg="#3B4252")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        tk.Label(main_frame, text="Vlastné hlasové príkazy", bg="#3B4252", fg="#D8DEE9",
                font=("Helvetica", 14, "bold")).pack(pady=10)

        # Štatistiky
        stats = self.custom_commands_manager.get_statistics()
        stats_text = f"Celkom príkazov: {stats['total_commands']} | Celkom použití: {stats['total_usage']}"
        tk.Label(main_frame, text=stats_text, bg="#3B4252", fg="#88C0D0",
                font=("Helvetica", 10)).pack(pady=5)

        # Zoznam príkazov
        commands_frame = tk.Frame(main_frame, bg="#3B4252")
        commands_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        commands = self.custom_commands_manager.list_commands()
        if not commands:
            tk.Label(commands_frame, text="Žiadne vlastné príkazy nie sú definované",
                    bg="#3B4252", fg="#D8DEE9").pack(pady=20)
        else:
            for cmd in commands:
                cmd_frame = tk.Frame(commands_frame, bg="#4C566A", relief=tk.RAISED, bd=1)
                cmd_frame.pack(fill=tk.X, padx=5, pady=3)

                name_text = f"📢 {cmd.name}"
                tk.Label(cmd_frame, text=name_text, bg="#4C566A", fg="#D8DEE9",
                        font=("Helvetica", 11, "bold")).pack(anchor=tk.W, padx=10, pady=2)

                triggers_text = f"Triggery: {', '.join(cmd.triggers)}"
                tk.Label(cmd_frame, text=triggers_text, bg="#4C566A", fg="#88C0D0",
                        font=("Helvetica", 9)).pack(anchor=tk.W, padx=10)

                usage_text = f"Použité: {cmd.usage_count}x | Typ: {cmd.action_type}"
                tk.Label(cmd_frame, text=usage_text, bg="#4C566A", fg="#A3BE8C",
                        font=("Helvetica", 9)).pack(anchor=tk.W, padx=10, pady=2)

        # Tlačidlo na pridanie nového príkazu
        add_button = tk.Button(main_frame, text="➕ Pridať nový príkaz",
                              command=lambda: self._show_add_command_dialog(commands_window),
                              bg="#5E81AC", fg="white", font=("Helvetica", 10))
        add_button.pack(pady=10)

    def test_animations(self):
        """Testuje animácie na GUI prvkoch."""
        # Animácia speech_text
        animated_speech = animate_widget(self.speech_text)
        animated_speech.pulse(1.0, 2)

        # Animácia tts_text
        animated_tts = animate_widget(self.tts_text)
        animated_tts.fade_in(0.5)

        # Animácia všetkých tlačidiel
        buttons = [self.mic_button, self.headphone_button, self.web_button,
                  self.plugins_button, self.commands_button, self.animate_button]

        for i, button in enumerate(buttons):
            animated_button = animate_widget(button)
            # Postupné animácie s oneskorením
            self.root.after(i * 200, lambda b=animated_button: b.pulse(0.5, 1))

        self.queue.put(("tts", "Testovanie animácií dokončené"))

    def _animate_window_fade_in(self, window):
        """Animácia fade-in pre okno."""
        def fade_step(alpha):
            if alpha <= 1.0:
                window.attributes('-alpha', alpha)
                window.after(20, lambda: fade_step(alpha + 0.05))

        fade_step(0.0)

    def _show_add_command_dialog(self, parent):
        """Zobrazí dialóg na pridanie nového príkazu."""
        dialog = tk.Toplevel(parent)
        dialog.title("➕ Pridať nový príkaz")
        dialog.geometry("500x400")
        dialog.configure(bg="#2E3440")

        # Pre jednoduchosť, zatiaľ iba informačné okno
        tk.Label(dialog, text="Pridávanie vlastných príkazov", bg="#2E3440", fg="#D8DEE9",
                font=("Helvetica", 14, "bold")).pack(pady=20)

        tk.Label(dialog, text="Táto funkcia bude dostupná v budúcej verzii.",
                bg="#2E3440", fg="#88C0D0", font=("Helvetica", 12)).pack(pady=10)

        tk.Label(dialog, text="Zatiaľ môžete používať webové rozhranie\nalebo upraviť súbor custom_commands.json",
                bg="#2E3440", fg="#A3BE8C", font=("Helvetica", 10)).pack(pady=10)

        tk.Button(dialog, text="OK", command=dialog.destroy,
                 bg="#5E81AC", fg="white", font=("Helvetica", 10)).pack(pady=20)

    def _load_enabled_plugins(self):
        """Načíta povolené pluginy."""
        try:
            for plugin in self.plugin_manager.list_plugins():
                if plugin.enabled:
                    success = self.plugin_manager.load_plugin(plugin.name)
                    if success:
                        logger.info(f"Plugin {plugin.name} úspešne načítaný")
                    else:
                        logger.warning(f"Plugin {plugin.name} sa nepodarilo načítať")
        except Exception as e:
            logger.error(f"Chyba pri načítavaní pluginov: {e}")

    def process_queue(self):
        """Spracovanie správ z fronty."""
        try:
            while not self.queue.empty():
                msg_type, msg = self.queue.get_nowait()
                if msg_type == "tts":
                    self.tts_text.insert(tk.END, f"{msg}\n")
                    self.tts_text.see(tk.END)
                    speak(msg, headphones_enabled=self.headphones_enabled)
                elif msg_type == "stt":
                    self.speech_text.insert(tk.END, f"Rozpoznaný príkaz: {msg}\n")
                    self.speech_text.see(tk.END)
                self.queue.task_done()
        except Queue.Empty:
            pass
        self.root.after(100, self.process_queue)

def main():
    """Hlavná funkcia aplikácie."""
    config = ConfigManager()
    # API kľúč sa načíta zo systémových premenných v deepseek_integration.py
    cap = cv2.VideoCapture(config.get("camera", "device_id"))
    cursor = Cursor()
    air_cursor = AirCursor()
    root = tk.Tk()
    queue = Queue()
    gui = ModernGUI(root, queue, cursor, air_cursor)

    def on_closing():
        air_cursor.running = False
        cursor.running = False
        cap.release()
        cv2.destroyAllWindows()
        queue.put(("tts", "Aplikácia bola ukončená."))
        speak("Aplikácia bola ukončená.", headphones_enabled=gui.headphones_enabled)
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    cursor_thread = threading.Thread(target=run_cursor_tracking, args=(air_cursor, cap, queue))
    voice_thread = threading.Thread(target=enhanced_voice_command_listener, args=(cursor, queue))
    cursor_thread.start()
    voice_thread.start()
    root.mainloop()
    cursor_thread.join()
    voice_thread.join()

if __name__ == "__main__":
    main()