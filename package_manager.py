#!/usr/bin/env python3
"""
AirCursor Assistant - Package Manager

Inteligentný package manager, ktor<PERSON>:
1. Detekuje operačný systém
2. Inštaluje systémové závislosti
3. Spravuje Python závislosti
4. Poskytuje update a cleanup funkcie

Podporované systémy:
- Windows (chocolatey, winget)
- macOS (homebrew)
- Linux (apt, yum, pacman)
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
from typing import List, Dict, Optional, Tuple

class Colors:
    """ANSI color codes pre farebný výstup."""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

class PackageManager:
    """Univerzálny package manager pre AirCursor Assistant."""

    def __init__(self):
        self.system = platform.system().lower()
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"

        # Systémové z<PERSON>ti pre každú platformu
        self.system_packages = {
            'windows': {
                'chocolatey': ['python312', 'git'],
                'winget': ['Python.Python.3.12', 'Git.Git']
            },
            'darwin': {  # macOS
                'homebrew': ['python@3.12', 'git', 'portaudio']
            },
            'linux': {
                'apt': ['python3.12', 'python3.12-pip', 'python3.12-venv', 'git', 'portaudio19-dev', 'python3-tk'],
                'yum': ['python3.12', 'python3.12-pip', 'git', 'portaudio-devel', 'tkinter'],
                'pacman': ['python312', 'python-pip', 'git', 'portaudio', 'tk']
            }
        }

    def print_status(self, message: str, status: str = "INFO"):
        """Vytlačí farebný status message."""
        colors = {
            "INFO": Colors.BLUE,
            "SUCCESS": Colors.GREEN,
            "WARNING": Colors.YELLOW,
            "ERROR": Colors.RED
        }
        color = colors.get(status, Colors.BLUE)
        print(f"{color}[{status}]{Colors.END} {message}")

    def run_command(self, command: List[str], check: bool = True) -> Tuple[bool, str]:
        """Spustí príkaz a vráti výsledok."""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=check
            )
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            return False, e.stderr
        except FileNotFoundError:
            return False, f"Príkaz nenájdený: {command[0]}"

    def detect_package_manager(self) -> Optional[str]:
        """Detekuje dostupný package manager."""
        managers = {
            'windows': ['choco', 'winget'],
            'darwin': ['brew'],
            'linux': ['apt-get', 'yum', 'pacman']
        }

        system_managers = managers.get(self.system, [])

        for manager in system_managers:
            if shutil.which(manager):
                return manager

        return None

    def install_system_packages(self) -> bool:
        """Inštaluje systémové závislosti."""
        self.print_status("Detekujem package manager...")

        manager = self.detect_package_manager()
        if not manager:
            self.print_status(
                f"Žiadny podporovaný package manager nenájdený pre {self.system}",
                "WARNING"
            )
            return True  # Nie je kritické

        self.print_status(f"Používam {manager}", "INFO")

        # Získaj zoznam balíčkov pre daný manager
        packages = []
        system_packages = self.system_packages.get(self.system, {})

        if manager == 'choco':
            packages = system_packages.get('chocolatey', [])
        elif manager == 'winget':
            packages = system_packages.get('winget', [])
        elif manager == 'brew':
            packages = system_packages.get('homebrew', [])
        elif manager in ['apt-get', 'apt']:
            packages = system_packages.get('apt', [])
        elif manager == 'yum':
            packages = system_packages.get('yum', [])
        elif manager == 'pacman':
            packages = system_packages.get('pacman', [])

        if not packages:
            self.print_status("Žiadne systémové balíčky na inštaláciu", "INFO")
            return True

        # Inštalácia balíčkov
        for package in packages:
            self.print_status(f"Inštalujem {package}...")

            if manager == 'choco':
                success, output = self.run_command(['choco', 'install', package, '-y'], check=False)
            elif manager == 'winget':
                success, output = self.run_command(['winget', 'install', package], check=False)
            elif manager == 'brew':
                success, output = self.run_command(['brew', 'install', package], check=False)
            elif manager in ['apt-get', 'apt']:
                success, output = self.run_command(['sudo', 'apt-get', 'install', '-y', package], check=False)
            elif manager == 'yum':
                success, output = self.run_command(['sudo', 'yum', 'install', '-y', package], check=False)
            elif manager == 'pacman':
                success, output = self.run_command(['sudo', 'pacman', '-S', '--noconfirm', package], check=False)
            else:
                continue

            if success:
                self.print_status(f"{package} nainštalovaný ✓", "SUCCESS")
            else:
                self.print_status(f"Chyba pri inštalácii {package}: {output}", "WARNING")

        return True

    def update_system_packages(self) -> bool:
        """Aktualizuje systémové balíčky."""
        self.print_status("Aktualizujem systémové balíčky...")

        manager = self.detect_package_manager()
        if not manager:
            return True

        if manager == 'choco':
            success, _ = self.run_command(['choco', 'upgrade', 'all', '-y'], check=False)
        elif manager == 'winget':
            success, _ = self.run_command(['winget', 'upgrade', '--all'], check=False)
        elif manager == 'brew':
            success, _ = self.run_command(['brew', 'update'], check=False)
            if success:
                success, _ = self.run_command(['brew', 'upgrade'], check=False)
        elif manager in ['apt-get', 'apt']:
            success, _ = self.run_command(['sudo', 'apt-get', 'update'], check=False)
            if success:
                success, _ = self.run_command(['sudo', 'apt-get', 'upgrade', '-y'], check=False)
        elif manager == 'yum':
            success, _ = self.run_command(['sudo', 'yum', 'update', '-y'], check=False)
        elif manager == 'pacman':
            success, _ = self.run_command(['sudo', 'pacman', '-Syu', '--noconfirm'], check=False)
        else:
            return True

        if success:
            self.print_status("Systémové balíčky aktualizované ✓", "SUCCESS")
        else:
            self.print_status("Chyba pri aktualizácii systémových balíčkov", "WARNING")

        return True

    def install_python_packages(self) -> bool:
        """Inštaluje Python závislosti."""
        requirements_file = self.project_root / "requirements.txt"

        if not requirements_file.exists():
            self.print_status("requirements.txt neexistuje", "ERROR")
            return False

        # Použije pip z virtual environment ak existuje
        if self.venv_path.exists():
            if platform.system() == "Windows":
                pip_path = self.venv_path / "Scripts" / "pip.exe"
            else:
                pip_path = self.venv_path / "bin" / "pip"
        else:
            pip_path = "pip"

        self.print_status("Inštalujem Python závislosti...")
        success, output = self.run_command([
            str(pip_path), 'install', '-r', str(requirements_file)
        ], check=False)

        if success:
            self.print_status("Python závislosti nainštalované ✓", "SUCCESS")
        else:
            self.print_status(f"Chyba pri inštalácii Python závislostí: {output}", "ERROR")

        return success

    def update_python_packages(self) -> bool:
        """Aktualizuje Python závislosti."""
        # Použije pip z virtual environment ak existuje
        if self.venv_path.exists():
            if platform.system() == "Windows":
                pip_path = self.venv_path / "Scripts" / "pip.exe"
            else:
                pip_path = self.venv_path / "bin" / "pip"
        else:
            pip_path = "pip"

        self.print_status("Aktualizujem Python závislosti...")

        # Získaj zoznam nainštalovaných balíčkov
        success, output = self.run_command([str(pip_path), 'list', '--outdated', '--format=freeze'], check=False)

        if not success:
            self.print_status("Chyba pri získavaní zoznamu balíčkov", "ERROR")
            return False

        outdated_packages = []
        for line in output.strip().split('\n'):
            if '==' in line:
                package_name = line.split('==')[0]
                outdated_packages.append(package_name)

        if not outdated_packages:
            self.print_status("Všetky Python balíčky sú aktuálne ✓", "SUCCESS")
            return True

        # Aktualizuj balíčky
        for package in outdated_packages:
            self.print_status(f"Aktualizujem {package}...")
            success, _ = self.run_command([str(pip_path), 'install', '--upgrade', package], check=False)

            if success:
                self.print_status(f"{package} aktualizovaný ✓", "SUCCESS")
            else:
                self.print_status(f"Chyba pri aktualizácii {package}", "WARNING")

        return True

    def cleanup(self) -> bool:
        """Vyčistí nepotrebné súbory a cache."""
        self.print_status("Čistím cache a dočasné súbory...")

        # Vyčisti pip cache
        if self.venv_path.exists():
            if platform.system() == "Windows":
                pip_path = self.venv_path / "Scripts" / "pip.exe"
            else:
                pip_path = self.venv_path / "bin" / "pip"
        else:
            pip_path = "pip"

        success, _ = self.run_command([str(pip_path), 'cache', 'purge'], check=False)

        # Vymaž dočasné súbory
        temp_files = [
            self.project_root / "temp_audio.mp3",
            self.project_root / "__pycache__",
            self.project_root / "*.pyc"
        ]

        for temp_file in temp_files:
            if temp_file.exists():
                if temp_file.is_dir():
                    shutil.rmtree(temp_file, ignore_errors=True)
                else:
                    temp_file.unlink(missing_ok=True)

        self.print_status("Cleanup dokončený ✓", "SUCCESS")
        return True

def main():
    """Hlavná funkcia package managera."""
    import argparse

    parser = argparse.ArgumentParser(description="AirCursor Assistant Package Manager")
    parser.add_argument('action', choices=['install', 'update', 'cleanup'],
                       help='Akcia na vykonanie')
    parser.add_argument('--system-only', action='store_true',
                       help='Iba systémové balíčky')
    parser.add_argument('--python-only', action='store_true',
                       help='Iba Python balíčky')

    args = parser.parse_args()

    pm = PackageManager()

    print(f"{Colors.BOLD}📦 AirCursor Assistant - Package Manager{Colors.END}")
    print("=" * 50)

    success = True

    if args.action == 'install':
        if not args.python_only:
            success &= pm.install_system_packages()
        if not args.system_only:
            success &= pm.install_python_packages()

    elif args.action == 'update':
        if not args.python_only:
            success &= pm.update_system_packages()
        if not args.system_only:
            success &= pm.update_python_packages()

    elif args.action == 'cleanup':
        success &= pm.cleanup()

    if success:
        pm.print_status("Operácia dokončená úspešne! 🎉", "SUCCESS")
    else:
        pm.print_status("Operácia dokončená s chybami", "WARNING")

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
