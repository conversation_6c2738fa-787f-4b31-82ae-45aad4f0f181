#!/usr/bin/env python3
"""
Plugin systém pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>í<PERSON>vanie a spravovanie pluginov tretích strán.
"""

import os
import sys
import json
import importlib
import importlib.util
import inspect
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Any, Optional, Type, Callable
import logging

logger = logging.getLogger(__name__)

class PluginInterface(ABC):
    """Základné rozhranie pre všetky pluginy."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Názov pluginu."""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Verzia pluginu."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Popis pluginu."""
        pass
    
    @property
    @abstractmethod
    def author(self) -> str:
        """Autor pluginu."""
        pass
    
    @abstractmethod
    def initialize(self, context: Dict[str, Any]) -> bool:
        """Inicializuje plugin. Vráti True ak úspešne."""
        pass
    
    @abstractmethod
    def cleanup(self) -> bool:
        """Vyčistí zdroje pluginu. Vráti True ak úspešne."""
        pass
    
    def get_commands(self) -> Dict[str, Callable]:
        """Vráti slovník príkazov, ktoré plugin poskytuje."""
        return {}
    
    def get_voice_commands(self) -> Dict[str, Callable]:
        """Vráti slovník hlasových príkazov."""
        return {}
    
    def get_gestures(self) -> Dict[str, Callable]:
        """Vráti slovník gest."""
        return {}
    
    def on_hand_detected(self, hand_landmarks) -> Optional[Any]:
        """Callback pri detekcii ruky."""
        return None
    
    def on_voice_command(self, command: str) -> Optional[Any]:
        """Callback pri hlasovom príkaze."""
        return None
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Vráti schému konfigurácie pluginu."""
        return {}

class PluginMetadata:
    """Metadáta pluginu."""
    
    def __init__(self, name: str, version: str, description: str, author: str,
                 file_path: str, dependencies: List[str] = None,
                 min_aircursor_version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.description = description
        self.author = author
        self.file_path = file_path
        self.dependencies = dependencies or []
        self.min_aircursor_version = min_aircursor_version
        self.enabled = True
        self.loaded = False
        self.instance: Optional[PluginInterface] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Konvertuje na slovník."""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'author': self.author,
            'file_path': self.file_path,
            'dependencies': self.dependencies,
            'min_aircursor_version': self.min_aircursor_version,
            'enabled': self.enabled,
            'loaded': self.loaded
        }

class PluginManager:
    """Správca pluginov."""
    
    def __init__(self, plugins_dir: str = "plugins"):
        self.plugins_dir = Path(plugins_dir)
        self.plugins: Dict[str, PluginMetadata] = {}
        self.loaded_plugins: Dict[str, PluginInterface] = {}
        self.plugin_config_file = self.plugins_dir / "plugins_config.json"
        
        # Vytvorenie adresára pre pluginy
        self.plugins_dir.mkdir(exist_ok=True)
        
        # Načítanie konfigurácie
        self.load_config()
    
    def load_config(self):
        """Načíta konfiguráciu pluginov."""
        if not self.plugin_config_file.exists():
            return
        
        try:
            with open(self.plugin_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            for plugin_data in config.get('plugins', []):
                metadata = PluginMetadata(**plugin_data)
                self.plugins[metadata.name] = metadata
                
        except Exception as e:
            logger.error(f"Chyba pri načítaní konfigurácie pluginov: {e}")
    
    def save_config(self):
        """Uloží konfiguráciu pluginov."""
        try:
            config = {
                'version': '1.0',
                'plugins': [plugin.to_dict() for plugin in self.plugins.values()]
            }
            
            with open(self.plugin_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Chyba pri ukladaní konfigurácie pluginov: {e}")
    
    def discover_plugins(self):
        """Objaví nové pluginy v adresári."""
        logger.info(f"Hľadám pluginy v {self.plugins_dir}")
        
        for file_path in self.plugins_dir.glob("*.py"):
            if file_path.name.startswith("__"):
                continue
            
            try:
                metadata = self._extract_plugin_metadata(file_path)
                if metadata and metadata.name not in self.plugins:
                    self.plugins[metadata.name] = metadata
                    logger.info(f"Objavený nový plugin: {metadata.name}")
                    
            except Exception as e:
                logger.error(f"Chyba pri analýze pluginu {file_path}: {e}")
        
        self.save_config()
    
    def _extract_plugin_metadata(self, file_path: Path) -> Optional[PluginMetadata]:
        """Extrahuje metadáta z plugin súboru."""
        try:
            # Načítanie modulu
            spec = importlib.util.spec_from_file_location("temp_plugin", file_path)
            if not spec or not spec.loader:
                return None
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Hľadanie triedy implementujúcej PluginInterface
            plugin_class = None
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (obj != PluginInterface and 
                    issubclass(obj, PluginInterface) and 
                    not inspect.isabstract(obj)):
                    plugin_class = obj
                    break
            
            if not plugin_class:
                logger.warning(f"Plugin {file_path} neobsahuje platnú plugin triedu")
                return None
            
            # Vytvorenie dočasnej inštancie pre získanie metadát
            temp_instance = plugin_class()
            
            return PluginMetadata(
                name=temp_instance.name,
                version=temp_instance.version,
                description=temp_instance.description,
                author=temp_instance.author,
                file_path=str(file_path),
                dependencies=getattr(temp_instance, 'dependencies', []),
                min_aircursor_version=getattr(temp_instance, 'min_aircursor_version', '1.0.0')
            )
            
        except Exception as e:
            logger.error(f"Chyba pri extrahovaní metadát z {file_path}: {e}")
            return None
    
    def load_plugin(self, plugin_name: str, context: Dict[str, Any] = None) -> bool:
        """Načíta a inicializuje plugin."""
        if plugin_name not in self.plugins:
            logger.error(f"Plugin {plugin_name} neexistuje")
            return False
        
        metadata = self.plugins[plugin_name]
        
        if not metadata.enabled:
            logger.info(f"Plugin {plugin_name} je zakázaný")
            return False
        
        if metadata.loaded:
            logger.info(f"Plugin {plugin_name} je už načítaný")
            return True
        
        try:
            # Kontrola závislostí
            if not self._check_dependencies(metadata):
                logger.error(f"Plugin {plugin_name} má nesplnené závislosti")
                return False
            
            # Načítanie modulu
            spec = importlib.util.spec_from_file_location(
                f"plugin_{plugin_name}", metadata.file_path
            )
            if not spec or not spec.loader:
                logger.error(f"Nemožno načítať plugin {plugin_name}")
                return False
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Nájdenie plugin triedy
            plugin_class = None
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (obj != PluginInterface and 
                    issubclass(obj, PluginInterface) and 
                    not inspect.isabstract(obj)):
                    plugin_class = obj
                    break
            
            if not plugin_class:
                logger.error(f"Plugin {plugin_name} neobsahuje platnú plugin triedu")
                return False
            
            # Vytvorenie inštancie a inicializácia
            instance = plugin_class()
            
            if instance.initialize(context or {}):
                self.loaded_plugins[plugin_name] = instance
                metadata.loaded = True
                metadata.instance = instance
                logger.info(f"Plugin {plugin_name} úspešne načítaný")
                self.save_config()
                return True
            else:
                logger.error(f"Inicializácia pluginu {plugin_name} zlyhala")
                return False
                
        except Exception as e:
            logger.error(f"Chyba pri načítaní pluginu {plugin_name}: {e}")
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """Vyloží plugin."""
        if plugin_name not in self.loaded_plugins:
            return True
        
        try:
            plugin = self.loaded_plugins[plugin_name]
            plugin.cleanup()
            
            del self.loaded_plugins[plugin_name]
            
            if plugin_name in self.plugins:
                self.plugins[plugin_name].loaded = False
                self.plugins[plugin_name].instance = None
            
            logger.info(f"Plugin {plugin_name} úspešne vyložený")
            self.save_config()
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri vykladaní pluginu {plugin_name}: {e}")
            return False
    
    def enable_plugin(self, plugin_name: str) -> bool:
        """Povolí plugin."""
        if plugin_name not in self.plugins:
            return False
        
        self.plugins[plugin_name].enabled = True
        self.save_config()
        return True
    
    def disable_plugin(self, plugin_name: str) -> bool:
        """Zakáže plugin."""
        if plugin_name not in self.plugins:
            return False
        
        # Najprv vyložiť ak je načítaný
        if plugin_name in self.loaded_plugins:
            self.unload_plugin(plugin_name)
        
        self.plugins[plugin_name].enabled = False
        self.save_config()
        return True
    
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """Vráti načítaný plugin."""
        return self.loaded_plugins.get(plugin_name)
    
    def list_plugins(self) -> List[PluginMetadata]:
        """Vráti zoznam všetkých pluginov."""
        return list(self.plugins.values())
    
    def list_loaded_plugins(self) -> List[str]:
        """Vráti zoznam načítaných pluginov."""
        return list(self.loaded_plugins.keys())
    
    def _check_dependencies(self, metadata: PluginMetadata) -> bool:
        """Skontroluje závislosti pluginu."""
        for dependency in metadata.dependencies:
            try:
                importlib.import_module(dependency)
            except ImportError:
                logger.error(f"Chýba závislosť: {dependency}")
                return False
        return True
    
    def call_plugin_method(self, method_name: str, *args, **kwargs) -> Dict[str, Any]:
        """Zavolá metódu na všetkých načítaných pluginoch."""
        results = {}
        
        for plugin_name, plugin in self.loaded_plugins.items():
            if hasattr(plugin, method_name):
                try:
                    method = getattr(plugin, method_name)
                    result = method(*args, **kwargs)
                    if result is not None:
                        results[plugin_name] = result
                except Exception as e:
                    logger.error(f"Chyba pri volaní {method_name} na plugin {plugin_name}: {e}")
        
        return results

# Globálny plugin manager
_global_plugin_manager = None

def get_plugin_manager() -> PluginManager:
    """Vráti globálny plugin manager."""
    global _global_plugin_manager
    if _global_plugin_manager is None:
        _global_plugin_manager = PluginManager()
    return _global_plugin_manager

if __name__ == "__main__":
    # Test plugin systému
    manager = PluginManager("test_plugins")
    manager.discover_plugins()
    
    print(f"Nájdených {len(manager.plugins)} pluginov:")
    for plugin in manager.list_plugins():
        print(f"- {plugin.name} v{plugin.version} ({plugin.author})")
        print(f"  {plugin.description}")
        print(f"  Povolený: {plugin.enabled}, Načítaný: {plugin.loaded}")
        print()
