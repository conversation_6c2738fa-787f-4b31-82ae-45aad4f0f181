#!/usr/bin/env python3
"""
Real Weather Plugin pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON> skutočné weather dáta cez OpenWeatherMap API.
"""

import requests
import json
import os
from typing import Dict, Any, Optional
from datetime import datetime
import logging

# Import plugin interface
import sys
sys.path.append('..')
from plugin_system import PluginInterface

logger = logging.getLogger(__name__)

class RealWeatherPlugin(PluginInterface):
    """Plugin pre skutočné weather dáta."""
    
    @property
    def name(self) -> str:
        return "real_weather_plugin"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @property
    def description(self) -> str:
        return "Poskytuje skutočné weather dáta cez OpenWeatherMap API"
    
    @property
    def author(self) -> str:
        return "AirCursor Team"
    
    def __init__(self):
        self.api_key = None
        self.base_url = "http://api.openweathermap.org/data/2.5"
        self.default_city = "Bratislava"
        self.units = "metric"  # Celsius
        self.lang = "sk"  # Slovenčina
        
        # Cache pre weather dáta (5 minút)
        self.cache = {}
        self.cache_duration = 300  # 5 minút v sekundách
        
        # Hlasové príkazy
        self.voice_commands = [
            "aké je počasie",
            "počasie dnes", 
            "teplota vonku",
            "počasie v",
            "predpoveď počasia",
            "aké bude počasie",
            "vlhkosť vzduchu",
            "rýchlosť vetra",
            "tlak vzduchu"
        ]
    
    def initialize(self, context: Dict[str, Any]) -> bool:
        """Inicializuje plugin."""
        try:
            # Získanie API kľúča
            config = context.get('config', {})
            self.api_key = config.get('openweather_api_key') or os.getenv('OPENWEATHER_API_KEY')
            
            if not self.api_key:
                logger.warning("OpenWeatherMap API kľúč nie je nastavený. Použije sa demo režim.")
                return True  # Stále môžeme fungovať v demo režime
            
            # Nastavenie default mesta
            self.default_city = config.get('default_city', 'Bratislava')
            self.units = config.get('units', 'metric')
            self.lang = config.get('language', 'sk')
            
            # Test API kľúča
            if self.api_key:
                test_response = self._get_current_weather(self.default_city)
                if test_response:
                    logger.info(f"✅ OpenWeatherMap API kľúč je platný")
                    return True
                else:
                    logger.warning("❌ OpenWeatherMap API kľúč nie je platný")
            
            return True
            
        except Exception as e:
            logger.error(f"Chyba pri inicializácii real weather plugin: {e}")
            return False
    
    def cleanup(self) -> bool:
        """Vyčistí plugin."""
        self.cache.clear()
        logger.info("Real weather plugin vyčistený")
        return True
    
    def get_voice_commands(self) -> Dict[str, Any]:
        """Vráti hlasové príkazy."""
        return {cmd: self.on_voice_command for cmd in self.voice_commands}
    
    def on_voice_command(self, command: str) -> Optional[Dict[str, Any]]:
        """Spracuje hlasový príkaz."""
        try:
            command_lower = command.lower().strip()
            
            # Aktuálne počasie
            if any(trigger in command_lower for trigger in ["aké je počasie", "počasie dnes"]):
                weather_data = self._get_current_weather(self.default_city)
                if weather_data:
                    message = self._format_current_weather(weather_data)
                    return {
                        'message': message,
                        'data': weather_data,
                        'type': 'current_weather'
                    }
            
            # Teplota
            elif "teplota" in command_lower:
                weather_data = self._get_current_weather(self.default_city)
                if weather_data:
                    temp = weather_data['main']['temp']
                    feels_like = weather_data['main']['feels_like']
                    message = f"Aktuálna teplota je {temp:.1f}°C, pocitovo {feels_like:.1f}°C"
                    return {'message': message, 'data': weather_data, 'type': 'temperature'}
            
            # Počasie v konkrétnom meste
            elif "počasie v" in command_lower:
                city = self._extract_city(command_lower)
                weather_data = self._get_current_weather(city)
                if weather_data:
                    message = self._format_current_weather(weather_data, city)
                    return {'message': message, 'data': weather_data, 'type': 'city_weather'}
            
            # Predpoveď
            elif any(trigger in command_lower for trigger in ["predpoveď", "aké bude počasie"]):
                forecast_data = self._get_forecast(self.default_city)
                if forecast_data:
                    message = self._format_forecast(forecast_data)
                    return {'message': message, 'data': forecast_data, 'type': 'forecast'}
            
            # Vlhkosť
            elif "vlhkosť" in command_lower:
                weather_data = self._get_current_weather(self.default_city)
                if weather_data:
                    humidity = weather_data['main']['humidity']
                    message = f"Vlhkosť vzduchu je {humidity}%"
                    return {'message': message, 'data': weather_data, 'type': 'humidity'}
            
            # Vietor
            elif "vietor" in command_lower or "rýchlosť vetra" in command_lower:
                weather_data = self._get_current_weather(self.default_city)
                if weather_data:
                    wind_speed = weather_data.get('wind', {}).get('speed', 0)
                    message = f"Rýchlosť vetra je {wind_speed} m/s"
                    return {'message': message, 'data': weather_data, 'type': 'wind'}
            
            # Tlak
            elif "tlak" in command_lower:
                weather_data = self._get_current_weather(self.default_city)
                if weather_data:
                    pressure = weather_data['main']['pressure']
                    message = f"Atmosférický tlak je {pressure} hPa"
                    return {'message': message, 'data': weather_data, 'type': 'pressure'}
            
            return None
            
        except Exception as e:
            logger.error(f"Chyba pri spracovaní weather príkazu: {e}")
            return {'message': "Chyba pri získavaní weather informácií", 'error': str(e)}
    
    def _get_current_weather(self, city: str) -> Optional[Dict[str, Any]]:
        """Získa aktuálne počasie pre mesto."""
        if not self.api_key:
            return self._get_demo_weather(city)
        
        # Kontrola cache
        cache_key = f"current_{city}"
        if self._is_cached(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            url = f"{self.base_url}/weather"
            params = {
                'q': city,
                'appid': self.api_key,
                'units': self.units,
                'lang': self.lang
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # Uloženie do cache
            self.cache[cache_key] = {
                'data': data,
                'timestamp': datetime.now().timestamp()
            }
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Chyba pri získavaní weather dát: {e}")
            return self._get_demo_weather(city)
        except Exception as e:
            logger.error(f"Neočakávaná chyba: {e}")
            return self._get_demo_weather(city)
    
    def _get_forecast(self, city: str) -> Optional[Dict[str, Any]]:
        """Získa predpoveď počasia."""
        if not self.api_key:
            return self._get_demo_forecast(city)
        
        cache_key = f"forecast_{city}"
        if self._is_cached(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            url = f"{self.base_url}/forecast"
            params = {
                'q': city,
                'appid': self.api_key,
                'units': self.units,
                'lang': self.lang
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            self.cache[cache_key] = {
                'data': data,
                'timestamp': datetime.now().timestamp()
            }
            
            return data
            
        except Exception as e:
            logger.error(f"Chyba pri získavaní predpovede: {e}")
            return self._get_demo_forecast(city)
    
    def _format_current_weather(self, data: Dict[str, Any], city: str = None) -> str:
        """Formátuje aktuálne počasie do textu."""
        try:
            city_name = city or data['name']
            temp = data['main']['temp']
            feels_like = data['main']['feels_like']
            humidity = data['main']['humidity']
            description = data['weather'][0]['description']
            
            message = f"V meste {city_name} je {temp:.1f}°C, pocitovo {feels_like:.1f}°C. "
            message += f"{description.capitalize()}. Vlhkosť {humidity}%."
            
            if 'wind' in data:
                wind_speed = data['wind']['speed']
                message += f" Vietor {wind_speed} m/s."
            
            return message
            
        except Exception as e:
            logger.error(f"Chyba pri formátovaní weather dát: {e}")
            return "Chyba pri spracovaní weather informácií"
    
    def _format_forecast(self, data: Dict[str, Any]) -> str:
        """Formátuje predpoveď počasia."""
        try:
            forecasts = data['list'][:3]  # Prvé 3 predpovede
            city_name = data['city']['name']
            
            message = f"Predpoveď pre {city_name}: "
            
            for i, forecast in enumerate(forecasts):
                temp = forecast['main']['temp']
                description = forecast['weather'][0]['description']
                time_str = ["Dnes", "Zajtra", "Pozajtra"][i] if i < 3 else f"Za {i} dní"
                message += f"{time_str} {temp:.1f}°C, {description}. "
            
            return message
            
        except Exception as e:
            logger.error(f"Chyba pri formátovaní predpovede: {e}")
            return "Chyba pri spracovaní predpovede počasia"
    
    def _extract_city(self, command: str) -> str:
        """Extrahuje názov mesta z príkazu."""
        if " v " in command:
            parts = command.split(" v ")
            if len(parts) > 1:
                city = parts[1].strip()
                city = city.replace("?", "").replace(".", "").strip()
                return city.title()
        return self.default_city
    
    def _is_cached(self, cache_key: str) -> bool:
        """Kontroluje, či sú dáta v cache a sú platné."""
        if cache_key not in self.cache:
            return False
        
        cached_time = self.cache[cache_key]['timestamp']
        current_time = datetime.now().timestamp()
        
        return (current_time - cached_time) < self.cache_duration
    
    def _get_demo_weather(self, city: str) -> Dict[str, Any]:
        """Vráti demo weather dáta."""
        return {
            'name': city,
            'main': {
                'temp': 20.0,
                'feels_like': 22.0,
                'humidity': 65,
                'pressure': 1013
            },
            'weather': [{'description': 'jasno'}],
            'wind': {'speed': 3.5}
        }
    
    def _get_demo_forecast(self, city: str) -> Dict[str, Any]:
        """Vráti demo predpoveď."""
        return {
            'city': {'name': city},
            'list': [
                {'main': {'temp': 22.0}, 'weather': [{'description': 'slnečno'}]},
                {'main': {'temp': 18.0}, 'weather': [{'description': 'oblačno'}]},
                {'main': {'temp': 15.0}, 'weather': [{'description': 'dážď'}]}
            ]
        }

if __name__ == "__main__":
    # Test real weather pluginu
    plugin = RealWeatherPlugin()
    
    print("🧪 Test Real Weather Plugin")
    print("=" * 40)
    
    # Test inicializácie
    success = plugin.initialize({'config': {}})
    print(f"✅ Inicializácia: {'OK' if success else 'FAILED'}")
    
    # Test hlasových príkazov
    test_commands = [
        "aké je počasie",
        "teplota vonku",
        "počasie v Košiciach",
        "predpoveď počasia"
    ]
    
    for command in test_commands:
        result = plugin.on_voice_command(command)
        if result:
            print(f"✅ '{command}' -> {result['message']}")
        else:
            print(f"❌ '{command}' -> Žiadna odpoveď")
    
    # Cleanup
    plugin.cleanup()
    print("\n🎉 Test dokončený!")
