@echo off
REM AirCursor Assistant - Windows Run Script
REM Spustí aplikáciu s virtual environment

setlocal enabledelayedexpansion

echo.
echo =====================================
echo   AirCursor Assistant - Spustenie
echo =====================================
echo.

REM Kontrola virtual environment
if not exist "venv\Scripts\python.exe" (
    echo [ERROR] Virtual environment neexistuje
    echo Spustite najprv: install.bat
    pause
    exit /b 1
)

REM Kontrola API kľúča
if "%GEMINI_API_KEY%"=="" (
    echo [WARNING] GEMINI_API_KEY nie je nastavený
    echo.
    echo Možnosti:
    echo 1. Nastavte systémovú premennú: set GEMINI_API_KEY=your_key
    echo 2. Spustite setup: python setup_env.py
    echo 3. Vytvorte .env súbor
    echo.
    set /p choice="Pokračovať aj tak? (y/n): "
    if /i not "!choice!"=="y" (
        exit /b 1
    )
)

echo [INFO] Spúšťam AirCursor Assistant...
echo.

REM Aktivácia venv a spustenie
call venv\Scripts\activate.bat
python main.py

if errorlevel 1 (
    echo.
    echo [ERROR] Aplikácia sa ukončila s chybou
    pause
)

deactivate
