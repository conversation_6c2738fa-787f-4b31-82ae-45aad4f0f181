#!/bin/bash
# AirCursor Assistant - Unix Run Script
# Spustí aplikáciu s virtual environment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo
echo "====================================="
echo "  AirCursor Assistant - Spustenie"
echo "====================================="
echo

# Check virtual environment
if [ ! -f "venv/bin/python" ]; then
    print_error "Virtual environment neexistuje"
    echo "Spustite najprv: ./install.sh"
    exit 1
fi

# Determine Python command
if command -v python3.12 &> /dev/null; then
    PYTHON_CMD="python3.12"
else
    PYTHON_CMD="python3"
fi

# Check API key
if [ -z "$GEMINI_API_KEY" ]; then
    print_warning "GEMINI_API_KEY nie je nastavený"
    echo
    echo "Možnosti:"
    echo "1. Nastavte environment variable: export GEMINI_API_KEY=your_key"
    echo "2. Spustite setup: python3 setup_env.py"
    echo "3. Vytvorte .env súbor"
    echo
    read -p "Pokračovať aj tak? (y/n): " choice
    if [[ ! "$choice" =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

print_status "Spúšťam AirCursor Assistant..."
echo

# Activate venv and run
source venv/bin/activate
python main.py

if [ $? -ne 0 ]; then
    echo
    print_error "Aplikácia sa ukončila s chybou"
fi

deactivate
