#!/usr/bin/env python3
"""
Test runner pre AirCursor Assistant

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>ky typy testov s farebným výstupom a reportingom.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

# Farby pre terminál
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(text, color=Colors.WHITE):
    """Vytlačí farebný text."""
    print(f"{color}{text}{Colors.END}")

def print_header(text):
    """Vytlačí header s rámčekom."""
    print_colored("=" * 60, Colors.CYAN)
    print_colored(f"🚀 {text}", Colors.BOLD + Colors.CYAN)
    print_colored("=" * 60, Colors.CYAN)

def print_success(text):
    """Vytlačí úspešnú správu."""
    print_colored(f"✅ {text}", Colors.GREEN)

def print_error(text):
    """Vytlačí chybovú správu."""
    print_colored(f"❌ {text}", Colors.RED)

def print_warning(text):
    """Vytlačí varovnú správu."""
    print_colored(f"⚠️  {text}", Colors.YELLOW)

def print_info(text):
    """Vytlačí informačnú správu."""
    print_colored(f"ℹ️  {text}", Colors.BLUE)

def run_command(command, description, capture_output=False):
    """Spustí príkaz a vráti výsledok."""
    print_info(f"Spúšťam: {description}")
    print_colored(f"Príkaz: {' '.join(command)}", Colors.MAGENTA)
    
    start_time = time.time()
    
    try:
        if capture_output:
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            output = result.stdout
        else:
            result = subprocess.run(command, check=True)
            output = None
        
        end_time = time.time()
        duration = end_time - start_time
        
        print_success(f"{description} - PASSED ({duration:.2f}s)")
        return True, output
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print_error(f"{description} - FAILED ({duration:.2f}s)")
        if capture_output and e.stdout:
            print_colored("STDOUT:", Colors.YELLOW)
            print(e.stdout)
        if capture_output and e.stderr:
            print_colored("STDERR:", Colors.RED)
            print(e.stderr)
        
        return False, None
    except FileNotFoundError:
        print_error(f"{description} - COMMAND NOT FOUND")
        return False, None

def check_dependencies():
    """Skontroluje dostupnosť potrebných nástrojov."""
    print_header("Kontrola závislostí")
    
    dependencies = [
        (["python", "--version"], "Python"),
        (["pip", "--version"], "pip"),
        (["pytest", "--version"], "pytest"),
    ]
    
    all_ok = True
    for command, name in dependencies:
        success, output = run_command(command, f"Kontrola {name}", capture_output=True)
        if success and output:
            version = output.strip().split('\n')[0]
            print_colored(f"  {name}: {version}", Colors.GREEN)
        else:
            all_ok = False
    
    return all_ok

def run_unit_tests(verbose=False, coverage=False):
    """Spustí unit testy."""
    print_header("Unit testy")
    
    command = ["python", "-m", "pytest", "tests/"]
    
    if verbose:
        command.append("-v")
    
    if coverage:
        command.extend(["--cov=.", "--cov-report=term-missing", "--cov-report=html"])
    
    command.extend(["--tb=short", "-x"])  # Stop na prvej chybe
    
    return run_command(command, "Unit testy")

def run_integration_tests(verbose=False):
    """Spustí integračné testy."""
    print_header("Integračné testy")
    
    command = ["python", "-m", "pytest", "tests/test_integration.py"]
    
    if verbose:
        command.append("-v")
    
    command.extend(["--tb=short", "-m", "not hardware"])
    
    return run_command(command, "Integračné testy")

def run_performance_tests(verbose=False):
    """Spustí performance testy."""
    print_header("Performance testy")
    
    command = ["python", "-m", "pytest", "tests/test_performance.py"]
    
    if verbose:
        command.append("-v")
    
    command.extend(["--tb=short"])
    
    return run_command(command, "Performance testy")

def run_linting():
    """Spustí linting nástroje."""
    print_header("Code Quality - Linting")
    
    results = []
    
    # Flake8
    success, _ = run_command(["flake8", "."], "Flake8 linting")
    results.append(("Flake8", success))
    
    # Black check
    success, _ = run_command(["black", "--check", "--diff", "."], "Black formatting check")
    results.append(("Black", success))
    
    # isort check
    success, _ = run_command(["isort", "--check-only", "--diff", "."], "isort import sorting check")
    results.append(("isort", success))
    
    return results

def run_security_checks():
    """Spustí bezpečnostné kontroly."""
    print_header("Bezpečnostné kontroly")
    
    results = []
    
    # Bandit
    success, _ = run_command(["bandit", "-r", ".", "-f", "txt"], "Bandit security check")
    results.append(("Bandit", success))
    
    # Safety
    success, _ = run_command(["safety", "check"], "Safety dependency check")
    results.append(("Safety", success))
    
    return results

def run_type_checking():
    """Spustí type checking."""
    print_header("Type Checking")
    
    command = ["mypy", ".", "--ignore-missing-imports"]
    success, _ = run_command(command, "MyPy type checking")
    
    return [("MyPy", success)]

def generate_report(results):
    """Vygeneruje súhrn výsledkov."""
    print_header("Súhrn výsledkov")
    
    total_tests = 0
    passed_tests = 0
    
    for category, test_results in results.items():
        print_colored(f"\n📊 {category}:", Colors.BOLD + Colors.CYAN)
        
        if isinstance(test_results, bool):
            # Jednoduchý boolean výsledok
            total_tests += 1
            if test_results:
                passed_tests += 1
                print_success("PASSED")
            else:
                print_error("FAILED")
        elif isinstance(test_results, list):
            # Zoznam výsledkov
            for test_name, success in test_results:
                total_tests += 1
                if success:
                    passed_tests += 1
                    print_success(f"{test_name}: PASSED")
                else:
                    print_error(f"{test_name}: FAILED")
    
    # Celkový súhrn
    print_colored("\n" + "=" * 60, Colors.CYAN)
    print_colored(f"📈 CELKOVÝ SÚHRN", Colors.BOLD + Colors.CYAN)
    print_colored("=" * 60, Colors.CYAN)
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print_colored(f"Celkové testy: {total_tests}", Colors.WHITE)
    print_colored(f"Úspešné: {passed_tests}", Colors.GREEN)
    print_colored(f"Neúspešné: {total_tests - passed_tests}", Colors.RED)
    print_colored(f"Úspešnosť: {success_rate:.1f}%", Colors.BOLD + Colors.CYAN)
    
    if success_rate == 100:
        print_colored("\n🎉 Všetky testy prešli úspešne!", Colors.BOLD + Colors.GREEN)
        return True
    else:
        print_colored(f"\n💥 {total_tests - passed_tests} testov zlyhalo!", Colors.BOLD + Colors.RED)
        return False

def main():
    """Hlavná funkcia."""
    parser = argparse.ArgumentParser(description="Test runner pre AirCursor Assistant")
    parser.add_argument("--unit", action="store_true", help="Spustiť iba unit testy")
    parser.add_argument("--integration", action="store_true", help="Spustiť iba integračné testy")
    parser.add_argument("--performance", action="store_true", help="Spustiť iba performance testy")
    parser.add_argument("--quality", action="store_true", help="Spustiť iba code quality checks")
    parser.add_argument("--security", action="store_true", help="Spustiť iba security checks")
    parser.add_argument("--typing", action="store_true", help="Spustiť iba type checking")
    parser.add_argument("--all", action="store_true", help="Spustiť všetky testy (default)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose výstup")
    parser.add_argument("--coverage", "-c", action="store_true", help="Zapnúť code coverage")
    parser.add_argument("--no-deps", action="store_true", help="Preskočiť kontrolu závislostí")
    
    args = parser.parse_args()
    
    # Ak nie je špecifikovaný žiadny test, spustiť všetky
    if not any([args.unit, args.integration, args.performance, args.quality, args.security, args.typing]):
        args.all = True
    
    print_header("AirCursor Assistant - Test Runner")
    print_info(f"Python verzia: {sys.version}")
    print_info(f"Pracovný adresár: {os.getcwd()}")
    
    # Kontrola závislostí
    if not args.no_deps:
        if not check_dependencies():
            print_error("Niektoré závislosti chýbajú!")
            return 1
    
    # Spustenie testov
    results = {}
    
    if args.unit or args.all:
        success, _ = run_unit_tests(args.verbose, args.coverage)
        results["Unit testy"] = success
    
    if args.integration or args.all:
        success, _ = run_integration_tests(args.verbose)
        results["Integračné testy"] = success
    
    if args.performance or args.all:
        success, _ = run_performance_tests(args.verbose)
        results["Performance testy"] = success
    
    if args.quality or args.all:
        quality_results = run_linting()
        results["Code Quality"] = quality_results
    
    if args.security or args.all:
        security_results = run_security_checks()
        results["Security"] = security_results
    
    if args.typing or args.all:
        typing_results = run_type_checking()
        results["Type Checking"] = typing_results
    
    # Generovanie reportu
    all_passed = generate_report(results)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
