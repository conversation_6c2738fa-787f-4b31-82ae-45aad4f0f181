@echo off
REM AirCursor Assistant - Windows Setup Script
REM Nastavenie environment variables

setlocal enabledelayedexpansion

echo.
echo ==========================================
echo   AirCursor Assistant - Environment Setup
echo ==========================================
echo.

REM Kontrola Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python nie je dostupný
    echo Spustite najprv: install.bat
    pause
    exit /b 1
)

echo [INFO] Spúšťam environment setup...
python setup_env.py

echo.
echo [INFO] Setup dokončený!
pause
