#!/usr/bin/env python3
"""
AirCursor Assistant - Setup Configuration

Setup script pre Python package management a distribúciu.
Špecifikuje Python 3.12+ ako minimálnu požiadavku.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Načítanie README pre long description
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Načítanie requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() 
            for line in f 
            if line.strip() and not line.startswith('#') and not line.startswith('-')
        ]

setup(
    name="aircursor-assistant",
    version="1.2.0",
    author="AirCursor Team",
    author_email="<EMAIL>",
    description="Inteligentný asistent na ovládanie počítača pomocou gest ruky a hlasových príkazov",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/aircursor/aircursor-assistant",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
        "Topic :: Multimedia :: Graphics :: Capture :: Digital Camera",
        "Topic :: Multimedia :: Sound/Audio :: Speech",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: System :: Hardware :: Hardware Drivers",
    ],
    python_requires=">=3.12",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "windows": [
            "pycaw>=20230407",
            "comtypes>=1.1.14",
        ],
        "ocr": [
            "pytesseract>=0.3.10",
        ],
        "web": [
            "selenium>=4.15.0",
            "webdriver-manager>=4.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "aircursor=main:main",
            "aircursor-setup=setup_env:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.md", "*.txt", "*.png", "*.ico"],
    },
    keywords=[
        "hand-tracking",
        "voice-commands",
        "computer-vision",
        "accessibility",
        "ai-assistant",
        "gesture-control",
        "speech-recognition",
        "automation",
    ],
    project_urls={
        "Bug Reports": "https://github.com/aircursor/aircursor-assistant/issues",
        "Source": "https://github.com/aircursor/aircursor-assistant",
        "Documentation": "https://github.com/aircursor/aircursor-assistant/blob/main/README.md",
    },
)
