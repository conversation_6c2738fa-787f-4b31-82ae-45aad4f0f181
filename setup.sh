#!/bin/bash
# AirCursor Assistant - Unix Setup Script
# Nastavenie environment variables

# Colors
BLUE='\033[0;34m'
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo
echo "=========================================="
echo "  AirCursor Assistant - Environment Setup"
echo "=========================================="
echo

# Check Python
if command -v python3.12 &> /dev/null; then
    PYTHON_CMD="python3.12"
elif command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    print_error "Python nie je dostupný"
    echo "Spustite najprv: ./install.sh"
    exit 1
fi

print_status "Spúšťam environment setup..."
$PYTHON_CMD setup_env.py

echo
print_success "Setup dokončený!"
