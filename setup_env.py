#!/usr/bin/env python3
"""
Setup script pre AirCursor Assistant
<PERSON><PERSON><PERSON><PERSON> s nastavením environment variables a základnou konfiguráciou.

Vylepšená verzia s:
- Interaktívnym nastavením API kľúčov
- Validáciou vstupov
- Automatickým testovaním
- Backup a restore funkciami
"""

import json
import os
import getpass
import re
from pathlib import Path
from typing import Optional

class Colors:
    """ANSI color codes pre farebný výstup."""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

class EnvironmentSetup:
    """Trieda pre nastavenie environment variables."""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.env_file = self.project_root / ".env"
        self.env_example = self.project_root / ".env.example"
        self.config_file = self.project_root / "config.json"

    def print_status(self, message: str, status: str = "INFO"):
        """Vytlačí farebný status message."""
        colors = {
            "INFO": Colors.BLUE,
            "SUCCESS": Colors.GREEN,
            "WARNING": Colors.YELLOW,
            "ERROR": Colors.RED
        }
        color = colors.get(status, Colors.BLUE)
        print(f"{color}[{status}]{Colors.END} {message}")

def check_env_variables() -> list:
    """Kontrola potrebných environment variables."""
    required_vars = ["GEMINI_API_KEY"]
    missing_vars = []

    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    return missing_vars

def validate_gemini_api_key(api_key: str) -> bool:
    """Validuje formát Gemini API kľúča."""
    if not api_key:
        return False

    # Gemini API kľúče začínajú s "AIza" a majú ~39 znakov
    pattern = r'^AIza[A-Za-z0-9_-]{35}$'
    return bool(re.match(pattern, api_key))

def interactive_api_key_setup() -> Optional[str]:
    """Interaktívne nastavenie API kľúča."""
    setup = EnvironmentSetup()

    print(f"\n{Colors.BLUE}🔑 Nastavenie Gemini API kľúča{Colors.END}")
    print("=" * 40)

    print("1. Navštívte: https://makersuite.google.com/app/apikey")
    print("2. Vytvorte nový API kľúč")
    print("3. Skopírujte ho sem")
    print()

    while True:
        api_key = getpass.getpass("Zadajte Gemini API kľúč (vstup je skrytý): ").strip()

        if not api_key:
            setup.print_status("API kľúč nemôže byť prázdny", "ERROR")
            continue

        if validate_gemini_api_key(api_key):
            setup.print_status("API kľúč má správny formát ✓", "SUCCESS")
            return api_key
        else:
            setup.print_status("Neplatný formát API kľúča", "ERROR")
            retry = input("Skúsiť znovu? (y/n): ").lower()
            if retry != 'y':
                return None

def save_to_env_file(api_key: str) -> bool:
    """Uloží API kľúč do .env súboru."""
    setup = EnvironmentSetup()

    try:
        # Backup existujúceho .env súboru
        if setup.env_file.exists():
            backup_file = setup.env_file.with_suffix('.env.backup')
            setup.env_file.rename(backup_file)
            setup.print_status(f"Backup vytvorený: {backup_file.name}", "INFO")

        # Načítaj .env.example ako šablónu
        content = ""
        if setup.env_example.exists():
            content = setup.env_example.read_text(encoding='utf-8')

        # Nahraď placeholder
        content = content.replace('your_gemini_api_key_here', api_key)

        # Ak nie je šablóna, vytvor základný obsah
        if not content:
            content = f"# AirCursor Assistant Environment Variables\nGEMINI_API_KEY={api_key}\n"

        # Ulož do .env súboru
        setup.env_file.write_text(content, encoding='utf-8')
        setup.print_status(".env súbor vytvorený ✓", "SUCCESS")
        return True

    except Exception as e:
        setup.print_status(f"Chyba pri ukladaní .env súboru: {e}", "ERROR")
        return False

def test_api_key(api_key: str) -> bool:
    """Testuje API kľúč s Gemini API."""
    setup = EnvironmentSetup()
    setup.print_status("Testujem API kľúč...", "INFO")

    try:
        # Nastavíme dočasne environment variable
        original_key = os.environ.get('GEMINI_API_KEY')
        os.environ['GEMINI_API_KEY'] = api_key

        # Pokúsime sa importovať a inicializovať Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)

        # Jednoduchý test
        model = genai.GenerativeModel("gemini-2.0-flash")
        model.generate_content("Test")  # Testujeme iba či API funguje

        # Obnovíme pôvodný kľúč
        if original_key:
            os.environ['GEMINI_API_KEY'] = original_key
        else:
            os.environ.pop('GEMINI_API_KEY', None)

        setup.print_status("API kľúč funguje ✓", "SUCCESS")
        return True

    except Exception as e:
        setup.print_status(f"API kľúč nefunguje: {e}", "ERROR")
        return False

def create_env_file():
    """Vytvorí .env súbor ak neexistuje."""
    env_file = Path(".env")
    env_example = Path(".env.example")

    if not env_file.exists() and env_example.exists():
        print("Vytváram .env súbor z .env.example...")
        env_file.write_text(env_example.read_text(encoding="utf-8"))
        print("✅ .env súbor vytvorený. Prosím vyplňte svoje API kľúče.")
        return True
    elif env_file.exists():
        print("✅ .env súbor už existuje.")
        return False
    else:
        print("❌ .env.example súbor neexistuje.")
        return False

def main():
    """Hlavná funkcia setup scriptu."""
    setup = EnvironmentSetup()

    print(f"{Colors.BOLD}🚀 AirCursor Assistant - Environment Setup{Colors.END}")
    print("=" * 50)

    # Kontrola environment variables
    missing_vars = check_env_variables()

    if not missing_vars:
        setup.print_status("Všetky potrebné environment variables sú nastavené ✓", "SUCCESS")
        print(f"\n{Colors.BLUE}🎯 Pre spustenie aplikácie použite:{Colors.END}")
        print("   python main.py")
        return

    setup.print_status(f"Chýbajúce environment variables: {', '.join(missing_vars)}", "WARNING")

    print(f"\n{Colors.BLUE}📝 Možnosti nastavenia:{Colors.END}")
    print("1. Interaktívne nastavenie (odporúčané)")
    print("2. Manuálne nastavenie systémových premenných")
    print("3. Manuálne úprava .env súboru")

    choice = input("\nVyberte možnosť (1-3) alebo Enter pre možnosť 1: ").strip()

    if choice in ['', '1']:
        # Interaktívne nastavenie
        api_key = interactive_api_key_setup()
        if not api_key:
            setup.print_status("Setup zrušený", "WARNING")
            return

        # Uloženie do .env súboru
        if not save_to_env_file(api_key):
            setup.print_status("Chyba pri ukladaní konfigurácie", "ERROR")
            return

        # Test API kľúča
        test_choice = input("\nChcete otestovať API kľúč? (y/n): ").lower()
        if test_choice == 'y':
            if test_api_key(api_key):
                setup.print_status("Setup úspešne dokončený! 🎉", "SUCCESS")
            else:
                setup.print_status("API kľúč nefunguje, skontrolujte ho", "WARNING")

    elif choice == '2':
        # Manuálne systémové premenné
        print(f"\n{Colors.BLUE}📋 Nastavenie systémových premenných:{Colors.END}")
        print("Windows:")
        print("   set GEMINI_API_KEY=your_api_key_here")
        print("\nLinux/Mac:")
        print("   export GEMINI_API_KEY=your_api_key_here")
        print("\nPre trvalé nastavenie pridajte do ~/.bashrc alebo ~/.zshrc")

    elif choice == '3':
        # Manuálna úprava .env súboru
        if create_env_file():
            print(f"\n{Colors.BLUE}📝 Upravte .env súbor:{Colors.END}")
            print(f"   {setup.env_file}")
            print("   Nahraďte 'your_gemini_api_key_here' skutočným API kľúčom")

    print(f"\n{Colors.BLUE}📖 Návod na získanie Gemini API kľúča:{Colors.END}")
    print("   https://makersuite.google.com/app/apikey")

    print(f"\n{Colors.BLUE}🎯 Po nastavení spustite aplikáciu:{Colors.END}")
    print("   python main.py")

if __name__ == "__main__":
    main()
