#!/usr/bin/env python3
"""
Test API kľúča pre OpenWeatherMap
"""

def test_api_key():
    """Test API kľúča."""
    print("🧪 Test API kľúča f98c81adc492ff4ea3871b364025957b")
    print("=" * 50)
    
    try:
        import requests
        print("✅ Requests knižnica importovaná")
        
        API_KEY = "f98c81adc492ff4ea3871b364025957b"
        url = "http://api.openweathermap.org/data/2.5/weather"
        params = {
            'q': 'Bratislava',
            'appid': API_KEY,
            'units': 'metric',
            'lang': 'sk'
        }
        
        print("🔄 Volám OpenWeatherMap API...")
        response = requests.get(url, params=params, timeout=10)
        
        print(f"📡 HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            city = data['name']
            temp = data['main']['temp']
            feels_like = data['main']['feels_like']
            humidity = data['main']['humidity']
            description = data['weather'][0]['description']
            pressure = data['main']['pressure']
            wind_speed = data.get('wind', {}).get('speed', 0)
            
            print("✅ API kľúč je PLATNÝ!")
            print("🌤️ AKTUÁLNE POČASIE:")
            print(f"   📍 Mesto: {city}")
            print(f"   🌡️ Teplota: {temp}°C (pocitovo {feels_like}°C)")
            print(f"   💧 Vlhkosť: {humidity}%")
            print(f"   ☁️ Popis: {description}")
            print(f"   🌪️ Vietor: {wind_speed} m/s")
            print(f"   📊 Tlak: {pressure} hPa")
            
            # Test weather príkazov
            print("\n🎤 TEST WEATHER PRÍKAZOV:")
            
            def format_weather_response(command_type):
                if command_type == "current":
                    return f"V meste {city} je {temp:.1f}°C, pocitovo {feels_like:.1f}°C. {description.capitalize()}. Vlhkosť {humidity}%."
                elif command_type == "temperature":
                    return f"Aktuálna teplota je {temp:.1f}°C, pocitovo {feels_like:.1f}°C"
                elif command_type == "humidity":
                    return f"Vlhkosť vzduchu je {humidity}%"
                elif command_type == "wind":
                    return f"Rýchlosť vetra je {wind_speed} m/s"
                elif command_type == "pressure":
                    return f"Atmosférický tlak je {pressure} hPa"
            
            test_commands = [
                ("aké je počasie", "current"),
                ("teplota vonku", "temperature"),
                ("vlhkosť vzduchu", "humidity"),
                ("rýchlosť vetra", "wind"),
                ("tlak vzduchu", "pressure")
            ]
            
            for command, cmd_type in test_commands:
                response_text = format_weather_response(cmd_type)
                print(f"   🎤 '{command}' → {response_text}")
            
            return True
            
        elif response.status_code == 401:
            print("❌ API kľúč je NEPLATNÝ")
            print("💡 Skontrolujte API kľúč na https://openweathermap.org/api_keys")
            return False
            
        else:
            print(f"❌ API chyba: {response.status_code}")
            print(f"📄 Odpoveď: {response.text}")
            return False
            
    except ImportError:
        print("❌ Requests knižnica nie je nainštalovaná")
        print("💡 Nainštalujte: pip install requests")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Timeout - API server neodpovedá")
        print("💡 Skúste neskôr")
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ Chyba pripojenia")
        print("💡 Skontrolujte internetové pripojenie")
        return False
        
    except Exception as e:
        print(f"❌ Neočakávaná chyba: {e}")
        return False

def test_weather_function():
    """Test weather funkcie z main.py."""
    print("\n🔧 Test weather funkcie z main.py")
    print("=" * 40)
    
    try:
        # Import handle_weather_command z main.py
        import sys
        sys.path.append('.')
        
        # Simulácia handle_weather_command
        import requests
        
        API_KEY = "f98c81adc492ff4ea3871b364025957b"
        
        def get_real_weather(city="Bratislava"):
            try:
                url = "http://api.openweathermap.org/data/2.5/weather"
                params = {
                    'q': city,
                    'appid': API_KEY,
                    'units': 'metric',
                    'lang': 'sk'
                }
                response = requests.get(url, params=params, timeout=10)
                return response.json() if response.status_code == 200 else None
            except:
                return None
        
        # Test weather príkazov
        test_commands = [
            "aké je počasie",
            "teplota vonku",
            "počasie v Košiciach",
            "vlhkosť vzduchu"
        ]
        
        for command in test_commands:
            print(f"🎤 Test: '{command}'")
            
            if "aké je počasie" in command:
                weather_data = get_real_weather("Bratislava")
                if weather_data:
                    city = weather_data['name']
                    temp = weather_data['main']['temp']
                    feels_like = weather_data['main']['feels_like']
                    humidity = weather_data['main']['humidity']
                    description = weather_data['weather'][0]['description']
                    message = f"V meste {city} je {temp:.1f}°C, pocitovo {feels_like:.1f}°C. {description.capitalize()}. Vlhkosť {humidity}%."
                    print(f"   ✅ {message}")
                else:
                    print("   ❌ API nedostupné")
            
            elif "teplota" in command:
                weather_data = get_real_weather("Bratislava")
                if weather_data:
                    temp = weather_data['main']['temp']
                    feels_like = weather_data['main']['feels_like']
                    message = f"Aktuálna teplota je {temp:.1f}°C, pocitovo {feels_like:.1f}°C"
                    print(f"   ✅ {message}")
                else:
                    print("   ❌ API nedostupné")
            
            elif "počasie v" in command:
                weather_data = get_real_weather("Košice")
                if weather_data:
                    city = weather_data['name']
                    temp = weather_data['main']['temp']
                    feels_like = weather_data['main']['feels_like']
                    humidity = weather_data['main']['humidity']
                    description = weather_data['weather'][0]['description']
                    message = f"V meste {city} je {temp:.1f}°C, pocitovo {feels_like:.1f}°C. {description.capitalize()}. Vlhkosť {humidity}%."
                    print(f"   ✅ {message}")
                else:
                    print("   ❌ API nedostupné")
            
            elif "vlhkosť" in command:
                weather_data = get_real_weather("Bratislava")
                if weather_data:
                    humidity = weather_data['main']['humidity']
                    message = f"Vlhkosť vzduchu je {humidity}%"
                    print(f"   ✅ {message}")
                else:
                    print("   ❌ API nedostupné")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba pri teste weather funkcie: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test Real Weather API s vašim kľúčom")
    print("=" * 60)
    
    # Test 1: API kľúč
    api_success = test_api_key()
    
    # Test 2: Weather funkcia
    function_success = test_weather_function()
    
    # Súhrn
    print("\n📊 SÚHRN TESTOV")
    print("=" * 30)
    print(f"API kľúč: {'✅ FUNGUJE' if api_success else '❌ NEFUNGUJE'}")
    print(f"Weather funkcia: {'✅ FUNGUJE' if function_success else '❌ NEFUNGUJE'}")
    
    if api_success and function_success:
        print("\n🎉 VŠETKO FUNGUJE!")
        print("✅ Váš API kľúč je platný")
        print("✅ Weather funkcie sú pripravené")
        print("✅ Môžete spustiť: python main.py")
        print("🎤 Hlasové príkazy budú vracať skutočné weather dáta!")
    else:
        print("\n⚠️ NIEČO NEFUNGUJE")
        print("💡 Skontrolujte internetové pripojenie")
        print("💡 Skúste neskôr")
        print("💡 Demo režim stále funguje")
