#!/usr/bin/env python3
"""
Test súbor pre vylepšenú Gemini integráciu

Testuje nové funkcie deepseek_integration.py bez potreby skutočného API kľúča.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import json

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestGeminiIntegration(unittest.TestCase):
    """Test trieda pre vylepšenú Gemini integráciu."""

    def setUp(self):
        """Nastavenie pre každý test."""
        # Mock environment variable
        self.env_patcher = patch.dict(os.environ, {'GEMINI_API_KEY': 'test_key'})
        self.env_patcher.start()

        # Mock genai
        self.genai_patcher = patch('deepseek_integration.genai')
        self.mock_genai = self.genai_patcher.start()

        # Mock ConfigManager
        self.config_patcher = patch('deepseek_integration.ConfigManager')
        self.mock_config_manager = self.config_patcher.start()

        # Nastavenie mock konfigurácie
        mock_config = Mock()
        mock_config.get.side_effect = self._mock_config_get
        self.mock_config_manager.return_value = mock_config

        # Mock model
        self.mock_model = Mock()
        self.mock_genai.GenerativeModel.return_value = self.mock_model

    def tearDown(self):
        """Cleanup po testoch."""
        self.env_patcher.stop()
        self.genai_patcher.stop()
        self.config_patcher.stop()

    def _mock_config_get(self, *args, **kwargs):
        """Mock pre config.get() metódu."""
        if args == ("gemini",):
            return {
                "model": "gemini-2.0-flash",
                "timeout": 30,
                "max_response_length": 300,
                "context_memory": True,
                "max_history_length": 5
            }
        elif args == ("voice", "commands", "ask"):
            return ["spýtaj sa", "otázka", "povedz mi"]
        else:
            return kwargs.get("default", {})

    def test_gemini_api_initialization(self):
        """Test inicializácie GeminiAPI."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Overenie základných atribútov
        self.assertEqual(api.api_key, "test_key")
        self.assertEqual(api.timeout, 30)
        self.assertEqual(api.max_response_length, 300)
        self.assertTrue(api.context_memory)
        self.assertEqual(api.max_history_length, 5)
        self.assertEqual(len(api.conversation_history), 0)

        print("✅ GeminiAPI inicializácia funguje správne")

    def test_question_recognition(self):
        """Test rozpoznávania otázok."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Test otázok
        questions = [
            "Čo je Python?",
            "Ako funguje AI?",
            "Prečo je to dôležité?",
            "Kedy sa to stalo?",
            "Kde sa to nachádza?",
            "Kto to vymyslel?",
            "Koľko to stojí?",
            "Aký je rozdiel?",
            "Môžem to urobiť?",
            "Vysvetli mi to",
            "Definuj machine learning",
            "Čo znamená API?",
            "Spýtaj sa na počasie"
        ]

        for question in questions:
            self.assertTrue(api._is_question(question), f"'{question}' by mala byť rozpoznaná ako otázka")

        # Test nie-otázok
        commands = [
            "klikni",
            "hlasitosť na 50",
            "otvor google",
            "ulož súbor",
            "zatvor okno"
        ]

        for command in commands:
            self.assertFalse(api._is_question(command), f"'{command}' by nemala byť rozpoznaná ako otázka")

        print("✅ Rozpoznávanie otázok funguje správne")

    def test_conversation_history(self):
        """Test histórie konverzácie."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Pridanie do histórie
        api._add_to_history("Čo je Python?", "Python je programovací jazyk.")
        api._add_to_history("Ako sa učiť?", "Začnite s tutoriálmi.")

        # Overenie histórie
        self.assertEqual(len(api.conversation_history), 2)
        self.assertEqual(api.conversation_history[0]["user"], "Čo je Python?")
        self.assertEqual(api.conversation_history[1]["ai"], "Začnite s tutoriálmi.")

        # Test maximálnej dĺžky histórie
        api.max_history_length = 2
        api._add_to_history("Tretia otázka", "Tretia odpoveď")

        self.assertEqual(len(api.conversation_history), 2)
        self.assertEqual(api.conversation_history[0]["user"], "Ako sa učiť?")

        # Test vymazania histórie
        api.clear_conversation_history()
        self.assertEqual(len(api.conversation_history), 0)

        print("✅ História konverzácie funguje správne")

    def test_context_prompt_generation(self):
        """Test generovania kontextového promptu."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Bez histórie
        context = api._get_context_prompt()
        self.assertEqual(context, "")

        # S históriou
        api._add_to_history("Čo je AI?", "AI je umelá inteligencia.")
        api._add_to_history("Ako funguje?", "Používa algoritmy a dáta.")

        context = api._get_context_prompt()
        self.assertIn("KONTEXT PREDCHÁDZAJÚCEJ KONVERZÁCIE", context)
        self.assertIn("Čo je AI?", context)
        self.assertIn("AI je umelá inteligencia", context)

        print("✅ Kontextový prompt sa generuje správne")

    def test_response_cleaning(self):
        """Test čistenia a formátovania odpovede."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Test odstránenia markdown
        dirty_response = "**Python** je *programovací* jazyk. ###Používa sa na vývoj aplikácií."
        clean_response = api._clean_and_format_response(dirty_response)

        self.assertNotIn("**", clean_response)
        self.assertNotIn("*", clean_response)
        self.assertNotIn("###", clean_response)

        # Test skrátenia dlhej odpovede
        api.max_response_length = 50
        long_response = "Toto je veľmi dlhá odpoveď. " * 10
        short_response = api._clean_and_format_response(long_response)

        self.assertLessEqual(len(short_response), 60)  # S malou rezervou

        print("✅ Čistenie odpovede funguje správne")

    def test_json_extraction(self):
        """Test extrakcie JSON z odpovede."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Test správneho JSON s požadovanými kľúčmi
        response_text = 'Nejaký text {"intent": "test", "action_command": {"name": "click", "parameters": {}}} ďalší text'
        json_data = api._extract_json_response(response_text)

        self.assertIsNotNone(json_data)
        self.assertEqual(json_data["intent"], "test")
        self.assertEqual(json_data["action_command"]["name"], "click")

        # Test JSON bez požadovaných kľúčov
        incomplete_json = '{"some_key": "value"}'
        json_data = api._extract_json_response(incomplete_json)
        self.assertIsNone(json_data)

        # Test neplatného JSON
        invalid_response = "Žiadny JSON tu nie je"
        json_data = api._extract_json_response(invalid_response)
        self.assertIsNone(json_data)

        print("✅ Extrakcia JSON funguje správne")

    def test_friendly_error_messages(self):
        """Test používateľsky prívetivých chybových správ."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Test rôznych typov chýb
        test_cases = [
            ("quota exceeded", "Momentálne je AI asistent preťažený"),
            ("network error", "Problém s internetovým pripojením"),
            ("api error", "Problém s AI službou"),
            ("unknown error", "Nastala neočakávaná chyba")
        ]

        for error, expected_start in test_cases:
            friendly_msg = api._get_friendly_error_message(error)
            self.assertTrue(friendly_msg.startswith(expected_start))

        print("✅ Chybové správy sú používateľsky prívetivé")

    def test_question_processing(self):
        """Test spracovania otázok s mock odpoveďou."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Mock odpoveď
        mock_response = Mock()
        mock_response.text = "Python je programovací jazyk vytvorený Guido van Rossumom."
        self.mock_model.generate_content.return_value = mock_response

        # Test spracovania otázky
        result = api._process_question("Čo je Python?")

        self.assertEqual(result["status"], "success")
        self.assertIn("Python", result["message"])
        self.assertEqual(len(api.conversation_history), 1)

        print("✅ Spracovanie otázok funguje správne")

    def test_action_command_processing(self):
        """Test spracovania akčných príkazov s mock odpoveďou."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Mock JSON odpoveď
        mock_response = Mock()
        mock_response.text = '{"intent": "kliknutie myši", "action_command": {"name": "click", "parameters": {}}}'
        self.mock_model.generate_content.return_value = mock_response

        # Test spracovania príkazu
        result = api._process_action_command("klikni")

        self.assertEqual(result["status"], "success")
        self.assertIsInstance(result["message"], dict)
        self.assertEqual(result["message"]["intent"], "kliknutie myši")

        print("✅ Spracovanie akčných príkazov funguje správne")

    def test_conversation_summary(self):
        """Test súhrnu konverzácie."""
        from deepseek_integration import GeminiAPI

        api = GeminiAPI()

        # Prázdny súhrn
        summary = api.get_conversation_summary()
        self.assertEqual(summary["total_exchanges"], 0)
        self.assertTrue(summary["context_memory_enabled"])
        self.assertIsNone(summary["last_interaction"])

        # Súhrn s históriou
        api._add_to_history("test", "response")
        summary = api.get_conversation_summary()
        self.assertEqual(summary["total_exchanges"], 1)
        self.assertIsNotNone(summary["last_interaction"])

        print("✅ Súhrn konverzácie funguje správne")

def run_integration_tests():
    """Spustí integračné testy bez API kľúča."""
    print("🧪 Spúšťam integračné testy Gemini integrácie...")
    print("=" * 60)

    # Test importu
    try:
        with patch.dict(os.environ, {'GEMINI_API_KEY': 'test_key'}):
            with patch('deepseek_integration.genai'):
                with patch('deepseek_integration.ConfigManager'):
                    import deepseek_integration
                    print("✅ Import deepseek_integration.py - PASSED")
    except Exception as e:
        print(f"❌ Import deepseek_integration.py - FAILED: {e}")
        return False

    # Test základných funkcií
    try:
        from deepseek_integration import GeminiAPI
        print("✅ Import GeminiAPI triedy - PASSED")
    except Exception as e:
        print(f"❌ Import GeminiAPI triedy - FAILED: {e}")
        return False

    print("\n🎉 Všetky integračné testy prešli úspešne!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Test Gemini Integration")
    print("=" * 60)

    # Spustenie integračných testov
    integration_success = run_integration_tests()

    if integration_success:
        print("\n🧪 Spúšťam unit testy...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Integračné testy zlyhali, preskakujem unit testy")
        sys.exit(1)
