#!/usr/bin/env python3
"""
Test súbor pre main.py

Testuje základnú funkcionalitu hlavného modulu AirCursor Assistant.
Používa mock objekty pre testovanie bez skutočných závislostí.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from queue import Queue
import tkinter as tk

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestMainModule(unittest.TestCase):
    """Test trieda pre main.py modul."""
    
    def setUp(self):
        """Nastavenie pre každý test."""
        self.queue = Queue()
        
    def test_imports(self):
        """Test importov - kontrola, či sa moduly dajú importovať."""
        try:
            # Test základných Python modulov
            import tkinter as tk
            import threading
            import os
            import logging
            from queue import Queue
            
            print("✅ Základné Python moduly sa importovali úspešne")
            
        except ImportError as e:
            self.fail(f"Chyba pri importe základných modulov: {e}")
    
    @patch('main.cv2')
    @patch('main.gTTS')
    @patch('main.playsound')
    @patch('main.Image')
    def test_speak_function(self, mock_image, mock_playsound, mock_gtts, mock_cv2):
        """Test speak funkcie."""
        # Mock gTTS
        mock_tts_instance = Mock()
        mock_gtts.return_value = mock_tts_instance
        
        # Mock os.remove
        with patch('main.os.remove') as mock_remove:
            # Import main po nastavení mockov
            import main
            
            # Test s povolenými slúchadlami
            main.speak("Test text", headphones_enabled=True)
            
            # Overenie, že gTTS bolo zavolané
            mock_gtts.assert_called_once_with(text="Test text", lang="sk")
            mock_tts_instance.save.assert_called_once_with("temp_audio.mp3")
            mock_playsound.assert_called_once_with("temp_audio.mp3")
            mock_remove.assert_called_once_with("temp_audio.mp3")
            
            # Reset mockov
            mock_gtts.reset_mock()
            mock_tts_instance.reset_mock()
            mock_playsound.reset_mock()
            mock_remove.reset_mock()
            
            # Test so zakázanými slúchadlami
            main.speak("Test text", headphones_enabled=False)
            
            # Overenie, že nič nebolo zavolané
            mock_gtts.assert_not_called()
            mock_playsound.assert_not_called()
            mock_remove.assert_not_called()
            
            print("✅ speak() funkcia funguje správne")
    
    @patch('main.cv2')
    @patch('main.gTTS')
    @patch('main.playsound')
    @patch('main.Image')
    @patch('main.AirCursor')
    @patch('main.Cursor')
    @patch('main.ConfigManager')
    def test_modern_gui_creation(self, mock_config, mock_cursor, mock_air_cursor, 
                                mock_image, mock_playsound, mock_gtts, mock_cv2):
        """Test vytvorenia ModernGUI."""
        # Mock objekty
        mock_cursor_instance = Mock()
        mock_air_cursor_instance = Mock()
        mock_cursor.return_value = mock_cursor_instance
        mock_air_cursor.return_value = mock_air_cursor_instance
        
        # Mock Image.open pre ikony
        mock_image_instance = Mock()
        mock_image.open.return_value = mock_image_instance
        mock_image_instance.resize.return_value = mock_image_instance
        
        # Mock ImageTk
        with patch('main.ImageTk') as mock_imagetk:
            mock_imagetk.PhotoImage.return_value = Mock()
            
            # Import main po nastavení mockov
            import main
            
            # Vytvorenie root okna
            root = tk.Tk()
            
            try:
                # Test vytvorenia GUI
                gui = main.ModernGUI(root, self.queue, mock_cursor_instance, mock_air_cursor_instance)
                
                # Overenie základných atribútov
                self.assertEqual(gui.root, root)
                self.assertEqual(gui.queue, self.queue)
                self.assertEqual(gui.cursor, mock_cursor_instance)
                self.assertEqual(gui.air_cursor, mock_air_cursor_instance)
                self.assertTrue(gui.mic_enabled)
                self.assertTrue(gui.headphones_enabled)
                
                print("✅ ModernGUI sa vytvorilo úspešne")
                
            finally:
                root.destroy()
    
    @patch('main.cv2')
    @patch('main.gTTS')
    @patch('main.playsound')
    @patch('main.Image')
    def test_gui_toggle_functions(self, mock_image, mock_playsound, mock_gtts, mock_cv2):
        """Test toggle funkcií GUI."""
        # Mock objekty
        mock_cursor = Mock()
        mock_air_cursor = Mock()
        
        # Mock Image.open pre ikony
        mock_image_instance = Mock()
        mock_image.open.return_value = mock_image_instance
        mock_image_instance.resize.return_value = mock_image_instance
        
        with patch('main.ImageTk') as mock_imagetk:
            mock_imagetk.PhotoImage.return_value = Mock()
            
            import main
            
            root = tk.Tk()
            
            try:
                gui = main.ModernGUI(root, self.queue, mock_cursor, mock_air_cursor)
                
                # Test toggle mikrofónu
                initial_mic_state = gui.mic_enabled
                gui.toggle_mic()
                self.assertEqual(gui.mic_enabled, not initial_mic_state)
                self.assertEqual(mock_cursor.mic_enabled, gui.mic_enabled)
                
                # Test toggle slúchadiel
                initial_headphones_state = gui.headphones_enabled
                gui.toggle_headphones()
                self.assertEqual(gui.headphones_enabled, not initial_headphones_state)
                self.assertEqual(mock_cursor.headphones_enabled, gui.headphones_enabled)
                
                print("✅ Toggle funkcie fungujú správne")
                
            finally:
                root.destroy()
    
    @patch('main.cv2')
    @patch('main.gTTS')
    @patch('main.playsound')
    @patch('main.Image')
    def test_queue_processing(self, mock_image, mock_playsound, mock_gtts, mock_cv2):
        """Test spracovania queue správ."""
        mock_cursor = Mock()
        mock_air_cursor = Mock()
        
        # Mock Image a ImageTk
        mock_image_instance = Mock()
        mock_image.open.return_value = mock_image_instance
        mock_image_instance.resize.return_value = mock_image_instance
        
        with patch('main.ImageTk') as mock_imagetk:
            mock_imagetk.PhotoImage.return_value = Mock()
            
            import main
            
            root = tk.Tk()
            
            try:
                gui = main.ModernGUI(root, self.queue, mock_cursor, mock_air_cursor)
                
                # Pridanie správ do queue
                self.queue.put(("tts", "Test TTS správa"))
                self.queue.put(("stt", "Test STT správa"))
                
                # Manuálne spracovanie queue (bez after loop)
                gui.process_queue()
                
                # Overenie, že správy boli spracované
                tts_content = gui.tts_text.get("1.0", tk.END)
                stt_content = gui.speech_text.get("1.0", tk.END)
                
                self.assertIn("Test TTS správa", tts_content)
                self.assertIn("Test STT správa", stt_content)
                
                print("✅ Queue spracovanie funguje správne")
                
            finally:
                root.destroy()
    
    def test_configuration_loading(self):
        """Test načítania konfigurácie."""
        try:
            from config_manager import ConfigManager
            
            # Test vytvorenia ConfigManager
            config = ConfigManager()
            
            # Test základných get operácií
            camera_id = config.get("camera", "device_id", default=0)
            self.assertIsNotNone(camera_id)
            
            print("✅ Konfigurácia sa načítala úspešne")
            
        except Exception as e:
            print(f"⚠️  Konfigurácia sa nenačítala: {e}")
    
    def test_error_handling(self):
        """Test error handling v speak funkcii."""
        with patch('main.gTTS') as mock_gtts:
            with patch('main.logger') as mock_logger:
                # Nastavenie mock-u aby vyhodil výnimku
                mock_gtts.side_effect = Exception("Test error")
                
                import main
                
                # Test, že speak nepadne pri chybe
                try:
                    main.speak("Test", headphones_enabled=True)
                    # Overenie, že sa zalogovala chyba
                    mock_logger.error.assert_called()
                    print("✅ Error handling funguje správne")
                except Exception as e:
                    self.fail(f"speak() by nemala padnúť pri chybe: {e}")

def run_basic_tests():
    """Spustí základné testy bez závislostí."""
    print("🧪 Spúšťam základné testy pre main.py...")
    print("=" * 50)
    
    # Test 1: Import základných modulov
    try:
        import tkinter as tk
        import threading
        import os
        import logging
        from queue import Queue
        print("✅ Test 1: Základné Python moduly - PASSED")
    except ImportError as e:
        print(f"❌ Test 1: Základné Python moduly - FAILED: {e}")
        return False
    
    # Test 2: Tkinter funkcionalita
    try:
        root = tk.Tk()
        root.title("Test")
        root.geometry("100x100")
        root.destroy()
        print("✅ Test 2: Tkinter funkcionalita - PASSED")
    except Exception as e:
        print(f"❌ Test 2: Tkinter funkcionalita - FAILED: {e}")
        return False
    
    # Test 3: Queue funkcionalita
    try:
        q = Queue()
        q.put(("test", "message"))
        msg_type, msg = q.get_nowait()
        assert msg_type == "test"
        assert msg == "message"
        print("✅ Test 3: Queue funkcionalita - PASSED")
    except Exception as e:
        print(f"❌ Test 3: Queue funkcionalita - FAILED: {e}")
        return False
    
    # Test 4: Threading
    try:
        import threading
        def test_thread():
            pass
        thread = threading.Thread(target=test_thread)
        thread.start()
        thread.join()
        print("✅ Test 4: Threading funkcionalita - PASSED")
    except Exception as e:
        print(f"❌ Test 4: Threading funkcionalita - FAILED: {e}")
        return False
    
    print("=" * 50)
    print("🎉 Všetky základné testy prešli úspešne!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Test main.py")
    print("=" * 50)
    
    # Spustenie základných testov
    basic_success = run_basic_tests()
    
    if basic_success:
        print("\n🧪 Spúšťam pokročilé unit testy...")
        
        # Spustenie unit testov
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Základné testy zlyhali, preskakujem unit testy")
        sys.exit(1)
