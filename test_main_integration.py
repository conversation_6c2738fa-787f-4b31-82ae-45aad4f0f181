#!/usr/bin/env python3
"""
Integračný test pre main.py

Testuje, či sa main.py dá spustiť bez chýb (bez GUI).
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def test_main_import():
    """Test importu main.py modulu."""
    print("🧪 Test 1: Import main.py modulu...")
    
    try:
        # Pridanie project root do Python path
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # Test importu s mock-mi pre chýbajúce závislosti
        import unittest.mock as mock
        
        with mock.patch('main.cv2'):
            with mock.patch('main.gTTS'):
                with mock.patch('main.playsound'):
                    with mock.patch('main.Image'):
                        with mock.patch('main.ImageTk'):
                            import main
                            print("✅ main.py sa importoval úspešne")
                            return True
    except Exception as e:
        print(f"❌ Chyba pri importe main.py: {e}")
        return False

def test_main_functions():
    """Test základných funkcií v main.py."""
    print("🧪 Test 2: Základné funkcie main.py...")
    
    try:
        import unittest.mock as mock
        
        with mock.patch('main.cv2'):
            with mock.patch('main.gTTS') as mock_gtts:
                with mock.patch('main.playsound'):
                    with mock.patch('main.Image'):
                        with mock.patch('main.ImageTk'):
                            import main
                            
                            # Test speak funkcie
                            mock_tts = mock.Mock()
                            mock_gtts.return_value = mock_tts
                            
                            with mock.patch('main.os.remove'):
                                main.speak("Test", headphones_enabled=False)
                                # Ak sú slúchadlá vypnuté, TTS sa nemá volať
                                mock_gtts.assert_not_called()
                            
                            print("✅ Základné funkcie fungujú správne")
                            return True
    except Exception as e:
        print(f"❌ Chyba pri testovaní funkcií: {e}")
        return False

def test_dependencies_availability():
    """Test dostupnosti závislostí."""
    print("🧪 Test 3: Dostupnosť závislostí...")
    
    dependencies = [
        ('tkinter', 'Tkinter GUI'),
        ('threading', 'Threading podpora'),
        ('queue', 'Queue komunikácia'),
        ('logging', 'Logging systém'),
        ('os', 'OS operácie'),
        ('pathlib', 'Path operácie')
    ]
    
    available = []
    missing = []
    
    for dep, desc in dependencies:
        try:
            __import__(dep)
            available.append((dep, desc))
            print(f"  ✅ {dep} - {desc}")
        except ImportError:
            missing.append((dep, desc))
            print(f"  ❌ {dep} - {desc}")
    
    # Test voliteľných závislostí
    optional_deps = [
        ('cv2', 'OpenCV pre video'),
        ('mediapipe', 'Hand tracking'),
        ('speech_recognition', 'Rozpoznávanie reči'),
        ('gtts', 'Text-to-Speech'),
        ('PIL', 'Image processing'),
        ('pyautogui', 'GUI automatizácia')
    ]
    
    print("\n  📦 Voliteľné závislosti:")
    for dep, desc in optional_deps:
        try:
            __import__(dep)
            print(f"  ✅ {dep} - {desc}")
        except ImportError:
            print(f"  ⚠️  {dep} - {desc} (nie je nainštalované)")
    
    if missing:
        print(f"\n❌ Chýbajú základné závislosti: {[dep for dep, _ in missing]}")
        return False
    else:
        print(f"\n✅ Všetky základné závislosti sú dostupné ({len(available)} z {len(dependencies)})")
        return True

def test_config_files():
    """Test existencie konfiguračných súborov."""
    print("🧪 Test 4: Konfiguračné súbory...")
    
    project_root = Path(__file__).parent
    
    required_files = [
        ('config.json', 'Hlavná konfigurácia'),
        ('requirements.txt', 'Python závislosti'),
        ('README.md', 'Dokumentácia'),
    ]
    
    optional_files = [
        ('.env', 'Environment variables'),
        ('calibration.json', 'Kalibračné údaje'),
        ('microphone.png', 'Mikrofón ikona'),
        ('headphones.png', 'Slúchadlá ikona'),
    ]
    
    print("  📁 Povinné súbory:")
    missing_required = []
    for file, desc in required_files:
        file_path = project_root / file
        if file_path.exists():
            print(f"  ✅ {file} - {desc}")
        else:
            print(f"  ❌ {file} - {desc}")
            missing_required.append(file)
    
    print("\n  📁 Voliteľné súbory:")
    for file, desc in optional_files:
        file_path = project_root / file
        if file_path.exists():
            print(f"  ✅ {file} - {desc}")
        else:
            print(f"  ⚠️  {file} - {desc} (voliteľné)")
    
    if missing_required:
        print(f"\n❌ Chýbajú povinné súbory: {missing_required}")
        return False
    else:
        print(f"\n✅ Všetky povinné súbory existujú")
        return True

def test_python_version():
    """Test Python verzie."""
    print("🧪 Test 5: Python verzia...")
    
    current_version = sys.version_info
    required_version = (3, 12)
    
    print(f"  🐍 Aktuálna verzia: Python {current_version.major}.{current_version.minor}.{current_version.micro}")
    print(f"  📋 Požadovaná verzia: Python {required_version[0]}.{required_version[1]}+")
    
    if current_version[:2] >= required_version:
        print("  ✅ Python verzia je kompatibilná")
        return True
    else:
        print(f"  ❌ Python verzia je príliš stará. Potrebujete {required_version[0]}.{required_version[1]}+")
        return False

def run_integration_tests():
    """Spustí všetky integračné testy."""
    print("🚀 AirCursor Assistant - Integračné testy main.py")
    print("=" * 60)
    
    tests = [
        ("Python verzia", test_python_version),
        ("Konfiguračné súbory", test_config_files),
        ("Dostupnosť závislostí", test_dependencies_availability),
        ("Import main.py", test_main_import),
        ("Základné funkcie", test_main_functions),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testov:")
    print(f"  ✅ Úspešné: {passed}")
    print(f"  ❌ Neúspešné: {failed}")
    print(f"  📈 Úspešnosť: {passed}/{passed + failed} ({100 * passed / (passed + failed):.1f}%)")
    
    if failed == 0:
        print("\n🎉 Všetky integračné testy prešli úspešne!")
        print("✅ main.py je pripravený na spustenie")
        return True
    else:
        print(f"\n⚠️  {failed} testov zlyhalo")
        print("🔧 Skontrolujte chyby a opravte ich pred spustením aplikácie")
        return False

def main():
    """Hlavná funkcia integračných testov."""
    success = run_integration_tests()
    
    if success:
        print("\n🚀 Odporúčané ďalšie kroky:")
        print("1. Nastavte GEMINI_API_KEY environment variable")
        print("2. Pripojte webkameru a mikrofón")
        print("3. Spustite aplikáciu: python main.py")
        print("4. Alebo použite: python run.py")
    else:
        print("\n🔧 Pred spustením aplikácie:")
        print("1. Opravte zlyhané testy")
        print("2. Nainštalujte chýbajúce závislosti: pip install -r requirements.txt")
        print("3. Spustite setup: python setup_env.py")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
