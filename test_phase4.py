#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> test pre overenie Fázy 4 implement<PERSON>cie

Spustí základn<PERSON> testy na overenie, že všetky nové súbory fungujú.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_colored(text, color_code):
    """Vytlačí farebný text."""
    print(f"\033[{color_code}m{text}\033[0m")

def print_success(text):
    print_colored(f"✅ {text}", "92")

def print_error(text):
    print_colored(f"❌ {text}", "91")

def print_info(text):
    print_colored(f"ℹ️  {text}", "94")

def print_header(text):
    print_colored(f"\n🚀 {text}", "96;1")
    print_colored("=" * 50, "96")

def check_file_exists(file_path, description):
    """Skontroluje, či súbor existuje."""
    if Path(file_path).exists():
        print_success(f"{description}: {file_path}")
        return True
    else:
        print_error(f"{description}: {file_path} - CHÝBA")
        return False

def test_imports():
    """Testuje importy nových test modulov."""
    print_header("Test importov")
    
    test_modules = [
        "tests.test_air_cursor",
        "tests.test_voice_commands", 
        "tests.test_screen_reader",
        "tests.test_integration",
        "tests.test_performance"
    ]
    
    success_count = 0
    for module in test_modules:
        try:
            __import__(module)
            print_success(f"Import {module}")
            success_count += 1
        except ImportError as e:
            print_error(f"Import {module}: {e}")
        except Exception as e:
            print_error(f"Import {module}: Neočakávaná chyba - {e}")
    
    return success_count == len(test_modules)

def test_pytest_discovery():
    """Testuje, či pytest nájde všetky testy."""
    print_header("Pytest test discovery")
    
    try:
        result = subprocess.run(
            ["python", "-m", "pytest", "--collect-only", "-q"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            test_count = len([line for line in lines if '::test_' in line])
            print_success(f"Pytest našiel {test_count} testov")
            return True
        else:
            print_error(f"Pytest collection failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print_error("Pytest collection timeout")
        return False
    except FileNotFoundError:
        print_error("Pytest nie je nainštalovaný")
        return False

def test_run_tests_script():
    """Testuje run_tests.py script."""
    print_header("Test run_tests.py")
    
    try:
        result = subprocess.run(
            ["python", "run_tests.py", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and "Test runner pre AirCursor Assistant" in result.stdout:
            print_success("run_tests.py script funguje")
            return True
        else:
            print_error(f"run_tests.py script failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print_error("run_tests.py timeout")
        return False
    except FileNotFoundError:
        print_error("Python nie je dostupný")
        return False

def main():
    """Hlavná funkcia."""
    print_header("Fáza 4 - Test implementácie")
    print_info("Overujem, že všetky nové súbory a funkcie fungujú...")
    
    # Test súborov
    print_header("Kontrola súborov")
    
    required_files = [
        ("tests/test_air_cursor.py", "AirCursor unit testy"),
        ("tests/test_voice_commands.py", "VoiceCommands unit testy"),
        ("tests/test_screen_reader.py", "ScreenReader unit testy"),
        ("tests/test_integration.py", "Integračné testy"),
        ("tests/test_performance.py", "Performance testy"),
        (".github/workflows/ci.yml", "CI/CD pipeline"),
        ("run_tests.py", "Test runner"),
        (".flake8", "Flake8 konfigurácia"),
        ("pyproject.toml", "Project konfigurácia"),
        ("PHASE4_TESTING_SUMMARY.md", "Fáza 4 dokumentácia")
    ]
    
    files_ok = 0
    for file_path, description in required_files:
        if check_file_exists(file_path, description):
            files_ok += 1
    
    # Test importov
    imports_ok = test_imports()
    
    # Test pytest discovery
    pytest_ok = test_pytest_discovery()
    
    # Test run_tests script
    script_ok = test_run_tests_script()
    
    # Súhrn
    print_header("Súhrn")
    
    total_checks = 4
    passed_checks = sum([
        files_ok == len(required_files),
        imports_ok,
        pytest_ok,
        script_ok
    ])
    
    print_info(f"Súbory: {files_ok}/{len(required_files)}")
    print_info(f"Importy: {'✅' if imports_ok else '❌'}")
    print_info(f"Pytest discovery: {'✅' if pytest_ok else '❌'}")
    print_info(f"Test runner: {'✅' if script_ok else '❌'}")
    
    success_rate = (passed_checks / total_checks) * 100
    
    if success_rate == 100:
        print_success(f"\n🎉 Fáza 4 je úspešne implementovaná! ({success_rate:.0f}%)")
        print_info("Môžete spustiť: python run_tests.py --all")
        return 0
    else:
        print_error(f"\n💥 Niektoré kontroly zlyhali ({success_rate:.0f}%)")
        print_info("Skontrolujte chyby vyššie a opravte ich")
        return 1

if __name__ == "__main__":
    sys.exit(main())
