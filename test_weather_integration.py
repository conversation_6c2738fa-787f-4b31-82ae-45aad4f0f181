#!/usr/bin/env python3
"""
Test integrácie weather pluginu s voice commands
"""

import sys
import os

def test_weather_plugin():
    """Test weather pluginu samostatne."""
    print("🧪 Test weather pluginu...")
    
    try:
        from plugin_system import PluginManager
        
        # Vytvorenie plugin managera
        pm = PluginManager()
        pm.discover_plugins()
        
        print(f"✅ Objavených {len(pm.list_plugins())} pluginov")
        
        # Načítanie weather pluginu
        success = pm.load_plugin('weather_plugin', {
            'config': {
                'weather_api_key': 'demo',
                'default_city': 'Bratislava'
            }
        })
        
        if success:
            print("✅ Weather plugin načítaný")
            
            # Test hlasového príkazu
            plugin = pm.get_plugin('weather_plugin')
            result = plugin.on_voice_command('aké je počasie')
            
            if result and 'message' in result:
                print(f"✅ Hlasový príkaz funguje: {result['message']}")
                return True
            else:
                print(f"❌ Hlasový príkaz nevrátil správnu odpoveď: {result}")
                return False
        else:
            print("❌ Weather plugin sa nepodarilo načítať")
            return False
            
    except Exception as e:
        print(f"❌ Chyba v weather plugin teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_voice_commands_import():
    """Test importu voice_commands."""
    print("\n🧪 Test voice_commands importu...")
    
    try:
        # Test postupného importu
        print("  Importujem plugin_system...")
        from plugin_system import get_plugin_manager
        print("  ✅ Plugin system OK")
        
        print("  Importujem custom_voice_commands...")
        from custom_voice_commands import CustomVoiceCommandManager
        print("  ✅ Custom commands OK")
        
        print("  Importujem voice_commands...")
        import voice_commands
        print("  ✅ Voice commands OK")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Chyba pri importe: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_integration():
    """Test manuálnej integrácie."""
    print("\n🧪 Test manuálnej integrácie...")
    
    try:
        # Import modulov
        from plugin_system import PluginManager
        from custom_voice_commands import CustomVoiceCommandManager
        
        # Vytvorenie manažérov
        plugin_manager = PluginManager()
        custom_commands_manager = CustomVoiceCommandManager()
        
        print("✅ Manažéri vytvorení")
        
        # Objavenie a načítanie pluginov
        plugin_manager.discover_plugins()
        success = plugin_manager.load_plugin('weather_plugin', {
            'config': {'weather_api_key': 'demo', 'default_city': 'Bratislava'}
        })
        
        if success:
            print("✅ Weather plugin načítaný")
            
            # Test hlasového príkazu cez plugin manager
            test_commands = [
                "aké je počasie",
                "počasie dnes", 
                "teplota vonku",
                "aké bude počasie"
            ]
            
            for command in test_commands:
                print(f"\n  Testujem príkaz: '{command}'")
                response = plugin_manager.call_plugin_method('on_voice_command', command)
                
                if response:
                    for plugin_name, result in response.items():
                        if result and isinstance(result, dict) and 'message' in result:
                            print(f"    ✅ {plugin_name}: {result['message']}")
                        else:
                            print(f"    ⚠️  {plugin_name}: {result}")
                else:
                    print(f"    ❌ Žiadna odpoveď")
            
            return True
        else:
            print("❌ Weather plugin sa nepodarilo načítať")
            return False
            
    except Exception as e:
        print(f"❌ Chyba v manuálnej integrácii: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Hlavná funkcia."""
    print("🚀 Test integrácie weather pluginu")
    print("=" * 50)
    
    # Test 1: Weather plugin samostatne
    test1 = test_weather_plugin()
    
    # Test 2: Voice commands import
    test2 = test_voice_commands_import()
    
    # Test 3: Manuálna integrácia
    test3 = test_manual_integration()
    
    # Súhrn
    print("\n📊 SÚHRN TESTOV")
    print("=" * 50)
    print(f"Weather plugin: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"Voice commands import: {'✅ PASSED' if test2 else '❌ FAILED'}")
    print(f"Manuálna integrácia: {'✅ PASSED' if test3 else '❌ FAILED'}")
    
    if test1 and test3:
        print("\n🎉 Weather plugin funguje! Problém je iba s voice_commands importom.")
        print("💡 Riešenie: Weather plugin je funkčný, ale voice_commands.py má import problém.")
        print("   Môžete použiť weather plugin priamo cez plugin manager.")
        return True
    elif test1:
        print("\n⚠️  Weather plugin funguje, ale integrácia má problémy.")
        return False
    else:
        print("\n❌ Weather plugin nefunguje.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
