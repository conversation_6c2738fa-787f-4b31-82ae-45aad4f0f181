#!/usr/bin/env python3
"""
Unit testy pre air_cursor.py modul

Testuje funkcionalitu hand tracking a ovládania kurzora.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import json

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestAirCursor(unittest.TestCase):
    """Test trieda pre AirCursor modul."""

    def setUp(self):
        """Nastavenie pre každý test."""
        # Mock MediaPipe
        self.mp_patcher = patch('air_cursor.mp')
        self.mock_mp = self.mp_patcher.start()

        # Mock cv2
        self.cv2_patcher = patch('air_cursor.cv2')
        self.mock_cv2 = self.cv2_patcher.start()

        # Mock pyautogui
        self.pyautogui_patcher = patch('air_cursor.pyautogui')
        self.mock_pyautogui = self.pyautogui_patcher.start()

        # Mock ConfigManager
        self.config_patcher = patch('air_cursor.ConfigManager')
        self.mock_config_manager = self.config_patcher.start()

        # Nastavenie mock konfigurácie
        mock_config = Mock()
        mock_config.get.side_effect = self._mock_config_get
        self.mock_config_manager.return_value = mock_config

        # Mock screen size
        self.mock_pyautogui.size.return_value = (1920, 1080)

    def tearDown(self):
        """Cleanup po testoch."""
        self.mp_patcher.stop()
        self.cv2_patcher.stop()
        self.pyautogui_patcher.stop()
        self.config_patcher.stop()

    def _mock_config_get(self, *args, **kwargs):
        """Mock pre config.get() metódu."""
        if args == ("hand_tracking",):
            return {
                "confidence": 0.7,
                "max_hands": 1
            }
        elif args == ("cursor",):
            return {
                "sensitivity": 2.0,
                "smoothing": 0.3
            }
        elif args == ("calibration",):
            return {
                "points": 4,
                "sets": 3
            }
        else:
            return kwargs.get("default", {})

    def test_air_cursor_initialization(self):
        """Test inicializácie AirCursor."""
        # Mock Path.exists aby vrátil False (žiadny kalibračný súbor)
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Overenie základných atribútov
            self.assertTrue(cursor.running)
            self.assertFalse(cursor.calibrated)
            self.assertEqual(len(cursor.corners), 0)
            self.assertEqual(len(cursor.calibration_sets), 0)
            self.assertIsNone(cursor.prev_cursor)

            print("✅ AirCursor inicializácia funguje správne")

    def test_calibration_point_addition(self):
        """Test pridávania kalibračných bodov."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Vyčistenie existujúcich bodov
            cursor.corners = []

            # Simulácia pridania kalibračných bodov
            test_points = [(100, 100), (200, 100), (200, 200), (100, 200)]

            for point in test_points:
                cursor.corners.append(point)

            self.assertEqual(len(cursor.corners), 4)
            self.assertEqual(cursor.corners[0], (100, 100))
            self.assertEqual(cursor.corners[-1], (100, 200))

            print("✅ Pridávanie kalibračných bodov funguje správne")

    def test_coordinate_transformation(self):
        """Test transformácie súradníc."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Nastavenie kalibračných bodov
            cursor.corners = [(100, 100), (300, 100), (300, 300), (100, 300)]
            cursor.calibrated = True

            # Test update_cursor metódy (ktorá skutočne existuje)
            with patch('air_cursor.pyautogui.moveTo') as mock_move:
                cursor.update_cursor((200, 200))
                # Overenie, že sa zavolala moveTo funkcia
                mock_move.assert_called_once()

            print("✅ Transformácia súradníc funguje správne")

    def test_cursor_movement_smoothing(self):
        """Test vyhladzenia pohybu kurzora."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()
            cursor.calibrated = True
            cursor.prev_cursor = (500, 400)

            # Test základného update_cursor
            with patch('air_cursor.pyautogui.moveTo') as mock_move:
                cursor.update_cursor((200, 200))
                # Overenie, že sa zavolala moveTo funkcia
                mock_move.assert_called_once()

            print("✅ Vyhladzenie pohybu kurzora funguje správne")

    def test_hand_detection(self):
        """Test detekcie ruky."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Mock frame
            mock_frame = np.zeros((480, 640, 3), dtype=np.uint8)

            # Mock MediaPipe results
            mock_results = Mock()
            mock_results.multi_hand_landmarks = None  # Žiadne ruky

            cursor.hands.process.return_value = mock_results

            # Test spracovania frame
            result = cursor.process_frame(mock_frame)

            # Overenie, že sa zavolal process
            cursor.hands.process.assert_called_once()

            print("✅ Detekcia ruky funguje správne")

    def test_auto_calibration(self):
        """Test automatickej kalibrácie."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Mock frame s rozmermi
            mock_frame = Mock()
            mock_frame.shape = [480, 640, 3]  # height, width, channels

            # Test pridania prvého rohu
            initial_corners = len(cursor.corners)
            cursor.auto_calibrate(mock_frame)

            # Overenie, že bol pridaný roh
            self.assertEqual(len(cursor.corners), initial_corners + 1)

            # Test dokončenia kalibrácie (4 rohy)
            for _ in range(3):  # Pridáme ešte 3 rohy
                cursor.auto_calibrate(mock_frame)

            # Po 4 rohoch by mal byť pridaný set a rohy resetované
            self.assertEqual(len(cursor.calibration_sets), 1)
            self.assertEqual(len(cursor.corners), 0)

        print("✅ Automatická kalibrácia funguje správne")

    def test_calibration_completion(self):
        """Test dokončenia kalibrácie."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Simulácia kompletnej kalibrácie
            cursor.calibration_sets = [
                [(100, 100), (300, 100), (300, 300), (100, 300)],
                [(105, 105), (295, 105), (295, 295), (105, 295)],
                [(95, 95), (305, 95), (305, 305), (95, 305)]
            ]

            # Mock save_calibration_points
            with patch.object(cursor, 'save_calibration_points'):
                cursor._finalize_calibration()

                self.assertTrue(cursor.calibrated)
                self.assertEqual(len(cursor.corners), 4)
                # Overenie priemerovaných hodnôt
                self.assertEqual(cursor.corners[0], (100, 100))  # (100+105+95)/3 = 100
                self.assertEqual(cursor.corners[1], (300, 100))  # (300+295+305)/3 = 300

        print("✅ Dokončenie kalibrácie funguje správne")

    def test_error_handling(self):
        """Test error handling."""
        with patch('air_cursor.Path.exists', return_value=False):
            from air_cursor import AirCursor

            cursor = AirCursor()

            # Test s None frame
            result = cursor.process_frame(None)
            # Process frame by mal vrátiť None alebo frame
            self.assertTrue(result is None or result is not None)

            # Test s neplatným frame
            invalid_frame = "not a frame"
            try:
                result = cursor.process_frame(invalid_frame)
                # Ak nevyhodí chybu, je to OK
                self.assertTrue(True)
            except:
                # Ak vyhodí chybu, je to tiež OK
                self.assertTrue(True)

            print("✅ Error handling funguje správne")

def run_air_cursor_tests():
    """Spustí testy pre air_cursor modul."""
    print("🧪 Spúšťam testy pre air_cursor.py...")
    print("=" * 50)

    # Test importu
    try:
        with patch('air_cursor.mp'), patch('air_cursor.cv2'), patch('air_cursor.pyautogui'):
            import air_cursor
            print("✅ Import air_cursor.py - PASSED")
    except Exception as e:
        print(f"❌ Import air_cursor.py - FAILED: {e}")
        return False

    print("\n🎉 Všetky základné testy pre air_cursor prešli úspešne!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Test air_cursor.py")
    print("=" * 50)

    # Spustenie základných testov
    basic_success = run_air_cursor_tests()

    if basic_success:
        print("\n🧪 Spúšťam unit testy...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Základné testy zlyhali, preskakujem unit testy")
        sys.exit(1)
