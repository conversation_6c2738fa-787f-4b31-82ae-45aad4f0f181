#!/usr/bin/env python3
"""
Integračné testy pre AirCursor Assistant

<PERSON><PERSON><PERSON> inter<PERSON><PERSON><PERSON> me<PERSON> modulmi a end-to-end funkcionalitu.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from queue import Queue
import threading
import time

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestIntegration(unittest.TestCase):
    """Test trieda pre integračné testy."""

    def setUp(self):
        """Nastavenie pre každý test."""
        self.queue = Queue()
        
        # Mock všetky externé závislosti
        self.patches = []
        
        # Mock CV2
        cv2_patch = patch('cv2.VideoCapture')
        self.mock_cv2_cap = cv2_patch.start()
        self.patches.append(cv2_patch)
        
        # Mock MediaPipe
        mp_patch = patch('air_cursor.mp')
        self.mock_mp = mp_patch.start()
        self.patches.append(mp_patch)
        
        # Mock PyAutoGUI
        pyautogui_patch = patch('pyautogui.size', return_value=(1920, 1080))
        self.mock_pyautogui = pyautogui_patch.start()
        self.patches.append(pyautogui_patch)
        
        # Mock Speech Recognition
        sr_patch = patch('voice_commands.sr')
        self.mock_sr = sr_patch.start()
        self.patches.append(sr_patch)
        
        # Mock TTS
        gtts_patch = patch('main.gTTS')
        self.mock_gtts = gtts_patch.start()
        self.patches.append(gtts_patch)
        
        playsound_patch = patch('main.playsound')
        self.mock_playsound = playsound_patch.start()
        self.patches.append(playsound_patch)
        
        # Mock Gemini API
        gemini_patch = patch('voice_commands.GeminiAPI')
        self.mock_gemini = gemini_patch.start()
        self.patches.append(gemini_patch)

    def tearDown(self):
        """Cleanup po testoch."""
        for patch_obj in self.patches:
            patch_obj.stop()

    def test_config_manager_integration(self):
        """Test integrácie ConfigManager s ostatnými modulmi."""
        try:
            from config_manager import ConfigManager
            
            # Test načítania konfigurácie
            config = ConfigManager()
            
            # Test základných get operácií
            camera_id = config.get("camera", "device_id", default=0)
            self.assertIsNotNone(camera_id)
            
            # Test s neexistujúcim kľúčom
            nonexistent = config.get("nonexistent", "key", default="default_value")
            self.assertEqual(nonexistent, "default_value")
            
            print("✅ ConfigManager integrácia funguje správne")
            
        except Exception as e:
            print(f"⚠️  ConfigManager integrácia: {e}")

    def test_air_cursor_voice_commands_integration(self):
        """Test integrácie medzi AirCursor a voice commands."""
        with patch('air_cursor.ConfigManager'), patch('voice_commands.json.load'):
            with patch('builtins.open'), patch('voice_commands.AudioUtilities'):
                try:
                    from air_cursor import AirCursor
                    from voice_commands import Cursor
                    
                    # Vytvorenie objektov
                    air_cursor = AirCursor()
                    voice_cursor = Cursor()
                    
                    # Test základných stavov
                    self.assertTrue(air_cursor.running)
                    self.assertTrue(voice_cursor.running)
                    
                    # Test komunikácie cez queue
                    self.queue.put(("tts", "Test správa"))
                    msg_type, msg = self.queue.get_nowait()
                    self.assertEqual(msg_type, "tts")
                    self.assertEqual(msg, "Test správa")
                    
                    print("✅ AirCursor-VoiceCommands integrácia funguje správne")
                    
                except Exception as e:
                    print(f"⚠️  AirCursor-VoiceCommands integrácia: {e}")

    def test_main_gui_integration(self):
        """Test integrácie hlavnej aplikácie s GUI."""
        with patch('main.cv2'), patch('main.Image'), patch('main.ImageTk'):
            with patch('main.AirCursor'), patch('main.Cursor'):
                with patch('main.ConfigManager'):
                    try:
                        import tkinter as tk
                        from main import ModernGUI
                        
                        # Mock objekty
                        mock_cursor = Mock()
                        mock_air_cursor = Mock()
                        
                        # Vytvorenie GUI
                        root = tk.Tk()
                        gui = ModernGUI(root, self.queue, mock_cursor, mock_air_cursor)
                        
                        # Test základných atribútov
                        self.assertIsNotNone(gui.root)
                        self.assertIsNotNone(gui.queue)
                        self.assertTrue(gui.mic_enabled)
                        self.assertTrue(gui.headphones_enabled)
                        
                        # Test toggle funkcií
                        initial_mic = gui.mic_enabled
                        gui.toggle_mic()
                        self.assertEqual(gui.mic_enabled, not initial_mic)
                        
                        root.destroy()
                        print("✅ Main-GUI integrácia funguje správne")
                        
                    except Exception as e:
                        print(f"⚠️  Main-GUI integrácia: {e}")

    def test_queue_communication(self):
        """Test komunikácie medzi vláknami cez Queue."""
        try:
            # Test rôznych typov správ
            test_messages = [
                ("tts", "Test TTS správa"),
                ("stt", "Test STT správa"),
                ("error", "Test chybová správa"),
                ("info", "Test info správa")
            ]
            
            # Pridanie správ do queue
            for msg_type, msg in test_messages:
                self.queue.put((msg_type, msg))
            
            # Čítanie správ z queue
            received_messages = []
            while not self.queue.empty():
                received_messages.append(self.queue.get_nowait())
            
            # Overenie, že všetky správy boli prijaté
            self.assertEqual(len(received_messages), len(test_messages))
            for i, (msg_type, msg) in enumerate(test_messages):
                self.assertEqual(received_messages[i], (msg_type, msg))
            
            print("✅ Queue komunikácia funguje správne")
            
        except Exception as e:
            print(f"⚠️  Queue komunikácia: {e}")

    def test_threading_integration(self):
        """Test integrácie threading komponentov."""
        try:
            # Mock funkcie pre vlákna
            def mock_cursor_tracking():
                time.sleep(0.1)
                self.queue.put(("info", "Cursor tracking aktívny"))
            
            def mock_voice_listener():
                time.sleep(0.1)
                self.queue.put(("info", "Voice listener aktívny"))
            
            # Vytvorenie a spustenie vlákien
            cursor_thread = threading.Thread(target=mock_cursor_tracking)
            voice_thread = threading.Thread(target=mock_voice_listener)
            
            cursor_thread.start()
            voice_thread.start()
            
            # Čakanie na dokončenie
            cursor_thread.join(timeout=1.0)
            voice_thread.join(timeout=1.0)
            
            # Overenie správ z vlákien
            messages = []
            while not self.queue.empty():
                messages.append(self.queue.get_nowait())
            
            self.assertEqual(len(messages), 2)
            
            print("✅ Threading integrácia funguje správne")
            
        except Exception as e:
            print(f"⚠️  Threading integrácia: {e}")

    def test_error_propagation(self):
        """Test šírenia chýb medzi modulmi."""
        try:
            # Test error handling v queue
            self.queue.put(("error", "Test chyba"))
            
            # Simulácia spracovania chyby
            msg_type, msg = self.queue.get_nowait()
            self.assertEqual(msg_type, "error")
            self.assertIn("chyba", msg.lower())
            
            # Test prázdnej queue
            with self.assertRaises(Exception):
                self.queue.get_nowait()
            
            print("✅ Error propagation funguje správne")
            
        except Exception as e:
            print(f"⚠️  Error propagation: {e}")

    def test_configuration_consistency(self):
        """Test konzistencie konfigurácie medzi modulmi."""
        try:
            from config_manager import ConfigManager
            
            # Test načítania rovnakej konfigurácie
            config1 = ConfigManager()
            config2 = ConfigManager()
            
            # Test, že oba objekty majú rovnaké dáta
            test_key = config1.get("camera", "device_id", default=0)
            test_key2 = config2.get("camera", "device_id", default=0)
            self.assertEqual(test_key, test_key2)
            
            print("✅ Configuration consistency funguje správne")
            
        except Exception as e:
            print(f"⚠️  Configuration consistency: {e}")

    def test_memory_management(self):
        """Test správy pamäte pri dlhodobom behu."""
        try:
            # Simulácia dlhodobého behu s queue
            for i in range(100):
                self.queue.put(("test", f"Správa {i}"))
            
            # Spracovanie všetkých správ
            processed = 0
            while not self.queue.empty():
                self.queue.get_nowait()
                processed += 1
            
            self.assertEqual(processed, 100)
            
            # Overenie, že queue je prázdna
            self.assertTrue(self.queue.empty())
            
            print("✅ Memory management funguje správne")
            
        except Exception as e:
            print(f"⚠️  Memory management: {e}")

def run_integration_tests():
    """Spustí integračné testy."""
    print("🧪 Spúšťam integračné testy...")
    print("=" * 50)
    
    # Test základných importov
    try:
        import tkinter as tk
        import threading
        from queue import Queue
        print("✅ Základné moduly - PASSED")
    except ImportError as e:
        print(f"❌ Základné moduly - FAILED: {e}")
        return False
    
    # Test project modulov
    try:
        with patch('cv2.VideoCapture'), patch('air_cursor.mp'):
            with patch('pyautogui.size', return_value=(1920, 1080)):
                from config_manager import ConfigManager
                print("✅ Project moduly - PASSED")
    except Exception as e:
        print(f"❌ Project moduly - FAILED: {e}")
        return False
    
    print("\n🎉 Všetky integračné testy prešli úspešne!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Integračné testy")
    print("=" * 50)
    
    # Spustenie základných testov
    basic_success = run_integration_tests()
    
    if basic_success:
        print("\n🧪 Spúšťam unit testy...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Základné testy zlyhali, preskakujem unit testy")
        sys.exit(1)
