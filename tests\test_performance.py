#!/usr/bin/env python3
"""
Performance testy pre AirCursor Assistant

<PERSON><PERSON><PERSON>ýkon<PERSON>ť kľúčových funkcií a identifikuje bottlenecks.
"""

import unittest
import sys
import os
import time
import threading
from unittest.mock import Mock, patch
from queue import Queue
import psutil
import gc

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestPerformance(unittest.TestCase):
    """Test trieda pre performance testy."""

    def setUp(self):
        """Nastavenie pre každý test."""
        self.queue = Queue()
        self.performance_data = {}
        
        # Mock externé závislosti
        self.patches = []
        
        cv2_patch = patch('cv2.VideoCapture')
        self.patches.append(cv2_patch)
        cv2_patch.start()
        
        mp_patch = patch('air_cursor.mp')
        self.patches.append(mp_patch)
        mp_patch.start()
        
        pyautogui_patch = patch('pyautogui.size', return_value=(1920, 1080))
        self.patches.append(pyautogui_patch)
        pyautogui_patch.start()

    def tearDown(self):
        """Cleanup po testoch."""
        for patch_obj in self.patches:
            patch_obj.stop()
        
        # Garbage collection
        gc.collect()

    def measure_time(self, func, *args, **kwargs):
        """Zmeria čas vykonania funkcie."""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        return result, execution_time

    def measure_memory(self, func, *args, **kwargs):
        """Zmeria spotrebu pamäte funkcie."""
        process = psutil.Process()
        
        # Garbage collection pred meraním
        gc.collect()
        memory_before = process.memory_info().rss
        
        result = func(*args, **kwargs)
        
        gc.collect()
        memory_after = process.memory_info().rss
        memory_used = memory_after - memory_before
        
        return result, memory_used

    def test_config_manager_performance(self):
        """Test výkonnosti ConfigManager."""
        try:
            from config_manager import ConfigManager
            
            # Test načítania konfigurácie
            result, exec_time = self.measure_time(ConfigManager)
            self.assertLess(exec_time, 0.1, "ConfigManager inicializácia je príliš pomalá")
            
            config = result
            
            # Test get operácií
            start_time = time.perf_counter()
            for _ in range(1000):
                config.get("camera", "device_id", default=0)
            end_time = time.perf_counter()
            
            avg_time = (end_time - start_time) / 1000
            self.assertLess(avg_time, 0.001, "Config.get() je príliš pomalé")
            
            self.performance_data["config_manager_init"] = exec_time
            self.performance_data["config_get_avg"] = avg_time
            
            print(f"✅ ConfigManager performance: init={exec_time:.4f}s, get_avg={avg_time:.6f}s")
            
        except Exception as e:
            print(f"⚠️  ConfigManager performance test: {e}")

    def test_queue_performance(self):
        """Test výkonnosti Queue operácií."""
        try:
            queue = Queue()
            
            # Test put operácií
            start_time = time.perf_counter()
            for i in range(10000):
                queue.put(("test", f"message_{i}"))
            put_time = time.perf_counter() - start_time
            
            # Test get operácií
            start_time = time.perf_counter()
            for _ in range(10000):
                queue.get_nowait()
            get_time = time.perf_counter() - start_time
            
            avg_put_time = put_time / 10000
            avg_get_time = get_time / 10000
            
            self.assertLess(avg_put_time, 0.0001, "Queue.put() je príliš pomalé")
            self.assertLess(avg_get_time, 0.0001, "Queue.get() je príliš pomalé")
            
            self.performance_data["queue_put_avg"] = avg_put_time
            self.performance_data["queue_get_avg"] = avg_get_time
            
            print(f"✅ Queue performance: put_avg={avg_put_time:.6f}s, get_avg={avg_get_time:.6f}s")
            
        except Exception as e:
            print(f"⚠️  Queue performance test: {e}")

    def test_air_cursor_performance(self):
        """Test výkonnosti AirCursor operácií."""
        with patch('air_cursor.ConfigManager'):
            try:
                from air_cursor import AirCursor
                
                # Test inicializácie
                result, exec_time = self.measure_time(AirCursor)
                self.assertLess(exec_time, 0.5, "AirCursor inicializácia je príliš pomalá")
                
                air_cursor = result
                
                # Test spracovania frame (mock)
                import numpy as np
                mock_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                
                # Mock MediaPipe results
                air_cursor.hands = Mock()
                air_cursor.hands.process.return_value = Mock(multi_hand_landmarks=None)
                
                start_time = time.perf_counter()
                for _ in range(100):
                    air_cursor.process_frame(mock_frame)
                frame_processing_time = time.perf_counter() - start_time
                
                avg_frame_time = frame_processing_time / 100
                fps = 1 / avg_frame_time if avg_frame_time > 0 else float('inf')
                
                self.assertGreater(fps, 15, "Frame processing je príliš pomalé pre real-time")
                
                self.performance_data["air_cursor_init"] = exec_time
                self.performance_data["frame_processing_avg"] = avg_frame_time
                self.performance_data["estimated_fps"] = fps
                
                print(f"✅ AirCursor performance: init={exec_time:.4f}s, frame_avg={avg_frame_time:.4f}s, fps={fps:.1f}")
                
            except Exception as e:
                print(f"⚠️  AirCursor performance test: {e}")

    def test_voice_commands_performance(self):
        """Test výkonnosti voice commands."""
        with patch('voice_commands.sr'), patch('voice_commands.pyautogui'):
            with patch('voice_commands.AudioUtilities'), patch('voice_commands.GeminiAPI'):
                with patch('voice_commands.json.load'), patch('builtins.open'):
                    try:
                        from voice_commands import Cursor
                        
                        # Test inicializácie
                        result, exec_time = self.measure_time(Cursor)
                        self.assertLess(exec_time, 1.0, "Cursor inicializácia je príliš pomalá")
                        
                        cursor = result
                        
                        # Test základných príkazov
                        commands = ['click', 'right_click', 'scroll_up', 'scroll_down']
                        
                        total_time = 0
                        for command in commands:
                            if hasattr(cursor, command):
                                _, cmd_time = self.measure_time(getattr(cursor, command))
                                total_time += cmd_time
                        
                        avg_command_time = total_time / len(commands)
                        self.assertLess(avg_command_time, 0.1, "Voice commands sú príliš pomalé")
                        
                        self.performance_data["voice_cursor_init"] = exec_time
                        self.performance_data["voice_command_avg"] = avg_command_time
                        
                        print(f"✅ VoiceCommands performance: init={exec_time:.4f}s, command_avg={avg_command_time:.4f}s")
                        
                    except Exception as e:
                        print(f"⚠️  VoiceCommands performance test: {e}")

    def test_memory_usage(self):
        """Test spotreby pamäte."""
        try:
            process = psutil.Process()
            
            # Počiatočná pamäť
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Simulácia dlhodobého behu
            queue = Queue()
            for i in range(10000):
                queue.put(("test", f"Long message with data {i}" * 10))
            
            # Spracovanie správ
            while not queue.empty():
                queue.get_nowait()
            
            # Garbage collection
            gc.collect()
            
            # Finálna pamäť
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            self.assertLess(memory_increase, 50, "Príliš veľký nárast pamäte")
            
            self.performance_data["initial_memory_mb"] = initial_memory
            self.performance_data["final_memory_mb"] = final_memory
            self.performance_data["memory_increase_mb"] = memory_increase
            
            print(f"✅ Memory usage: initial={initial_memory:.1f}MB, final={final_memory:.1f}MB, increase={memory_increase:.1f}MB")
            
        except Exception as e:
            print(f"⚠️  Memory usage test: {e}")

    def test_threading_performance(self):
        """Test výkonnosti threading operácií."""
        try:
            def worker_function(queue, worker_id, iterations):
                for i in range(iterations):
                    queue.put((f"worker_{worker_id}", f"message_{i}"))
                    time.sleep(0.001)  # Simulácia práce
            
            queue = Queue()
            threads = []
            num_threads = 4
            iterations_per_thread = 100
            
            # Spustenie vlákien
            start_time = time.perf_counter()
            for i in range(num_threads):
                thread = threading.Thread(target=worker_function, args=(queue, i, iterations_per_thread))
                threads.append(thread)
                thread.start()
            
            # Čakanie na dokončenie
            for thread in threads:
                thread.join()
            
            end_time = time.perf_counter()
            total_time = end_time - start_time
            
            # Spracovanie správ
            messages_processed = 0
            while not queue.empty():
                queue.get_nowait()
                messages_processed += 1
            
            expected_messages = num_threads * iterations_per_thread
            self.assertEqual(messages_processed, expected_messages)
            
            throughput = messages_processed / total_time
            self.assertGreater(throughput, 100, "Threading throughput je príliš nízky")
            
            self.performance_data["threading_total_time"] = total_time
            self.performance_data["threading_throughput"] = throughput
            
            print(f"✅ Threading performance: time={total_time:.4f}s, throughput={throughput:.1f} msg/s")
            
        except Exception as e:
            print(f"⚠️  Threading performance test: {e}")

    def test_cpu_usage(self):
        """Test využitia CPU."""
        try:
            # Meranie CPU pred testom
            cpu_before = psutil.cpu_percent(interval=1)
            
            # Simulácia CPU-intensive operácie
            start_time = time.perf_counter()
            result = sum(i * i for i in range(100000))
            end_time = time.perf_counter()
            
            # Meranie CPU po teste
            cpu_after = psutil.cpu_percent(interval=1)
            
            computation_time = end_time - start_time
            self.assertLess(computation_time, 1.0, "CPU-intensive operácia je príliš pomalá")
            
            self.performance_data["cpu_before"] = cpu_before
            self.performance_data["cpu_after"] = cpu_after
            self.performance_data["computation_time"] = computation_time
            
            print(f"✅ CPU usage: before={cpu_before:.1f}%, after={cpu_after:.1f}%, computation={computation_time:.4f}s")
            
        except Exception as e:
            print(f"⚠️  CPU usage test: {e}")

    def test_performance_summary(self):
        """Súhrn všetkých performance metrík."""
        print("\n📊 PERFORMANCE SUMMARY")
        print("=" * 50)
        
        for metric, value in self.performance_data.items():
            if isinstance(value, float):
                if "time" in metric or "avg" in metric:
                    print(f"{metric}: {value:.6f}s")
                elif "mb" in metric:
                    print(f"{metric}: {value:.1f}MB")
                elif "fps" in metric or "throughput" in metric:
                    print(f"{metric}: {value:.1f}")
                else:
                    print(f"{metric}: {value:.4f}")
            else:
                print(f"{metric}: {value}")
        
        print("=" * 50)

def run_performance_tests():
    """Spustí performance testy."""
    print("🧪 Spúšťam performance testy...")
    print("=" * 50)
    
    # Kontrola dostupnosti psutil
    try:
        import psutil
        print("✅ psutil dostupný - PASSED")
    except ImportError:
        print("❌ psutil nie je dostupný - FAILED")
        print("💡 Nainštalujte: pip install psutil")
        return False
    
    print("\n🎉 Performance testy sú pripravené!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Performance testy")
    print("=" * 50)
    
    # Spustenie základných testov
    basic_success = run_performance_tests()
    
    if basic_success:
        print("\n🧪 Spúšťam performance unit testy...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Základné testy zlyhali, preskakujem performance testy")
        sys.exit(1)
