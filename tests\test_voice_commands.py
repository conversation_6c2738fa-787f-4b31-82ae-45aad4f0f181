#!/usr/bin/env python3
"""
Unit testy pre voice_commands.py modul

Testuje funkcionalitu hlasových príkazov a ovládania kurzora.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from queue import Queue
import json

# Pridanie project root do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestVoiceCommands(unittest.TestCase):
    """Test trieda pre voice_commands modul."""

    def setUp(self):
        """Nastavenie pre každý test."""
        # Mock všetky závislosti
        self.sr_patcher = patch('voice_commands.sr')
        self.mock_sr = self.sr_patcher.start()
        
        self.pyautogui_patcher = patch('voice_commands.pyautogui')
        self.mock_pyautogui = self.pyautogui_patcher.start()
        
        self.webbrowser_patcher = patch('voice_commands.webbrowser')
        self.mock_webbrowser = self.webbrowser_patcher.start()
        
        self.gtts_patcher = patch('voice_commands.gTTS')
        self.mock_gtts = self.gtts_patcher.start()
        
        self.playsound_patcher = patch('voice_commands.playsound')
        self.mock_playsound = self.playsound_patcher.start()
        
        self.pycaw_patcher = patch('voice_commands.AudioUtilities')
        self.mock_pycaw = self.pycaw_patcher.start()
        
        self.gemini_patcher = patch('voice_commands.GeminiAPI')
        self.mock_gemini_class = self.gemini_patcher.start()
        
        self.screen_reader_patcher = patch('voice_commands.process_voice_command')
        self.mock_screen_reader = self.screen_reader_patcher.start()
        
        # Mock konfigurácia
        self.mock_config = {
            "voice": {
                "commands": {
                    "click": ["klikni", "klik"],
                    "right_click": ["pravý klik", "pravé tlačidlo"],
                    "scroll_up": ["posun hore", "scroll hore"],
                    "scroll_down": ["posun dole", "scroll dole"],
                    "volume_up": ["hlasitosť hore", "zvýš hlasitosť"],
                    "volume_down": ["hlasitosť dole", "zníž hlasitosť"],
                    "volume_set": ["hlasitosť na", "nastav hlasitosť"],
                    "open_application": ["otvor aplikáciu", "spusti"]
                }
            },
            "mouse": {
                "scroll_amount_up": 3,
                "scroll_amount_down": -3
            }
        }

    def tearDown(self):
        """Cleanup po testoch."""
        self.sr_patcher.stop()
        self.pyautogui_patcher.stop()
        self.webbrowser_patcher.stop()
        self.gtts_patcher.stop()
        self.playsound_patcher.stop()
        self.pycaw_patcher.stop()
        self.gemini_patcher.stop()
        self.screen_reader_patcher.stop()

    def test_cursor_initialization(self):
        """Test inicializácie Cursor triedy."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Overenie základných atribútov
                self.assertTrue(cursor.running)
                self.assertTrue(cursor.mic_enabled)
                self.assertTrue(cursor.headphones_enabled)
                self.assertIsNotNone(cursor.commands)
                self.assertIsNotNone(cursor.command_map)
                
                print("✅ Cursor inicializácia funguje správne")

    def test_basic_commands(self):
        """Test základných príkazov."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Test click
                cursor.click()
                self.mock_pyautogui.click.assert_called_once()
                
                # Test right click
                cursor.right_click()
                self.mock_pyautogui.rightClick.assert_called_once()
                
                # Test double click
                cursor.double_click()
                self.mock_pyautogui.doubleClick.assert_called_once()
                
                print("✅ Základné príkazy fungujú správne")

    def test_scroll_commands(self):
        """Test scroll príkazov."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Test scroll up
                cursor.scroll_up()
                self.mock_pyautogui.scroll.assert_called_with(3)
                
                # Test scroll down
                cursor.scroll_down()
                self.mock_pyautogui.scroll.assert_called_with(-3)
                
                print("✅ Scroll príkazy fungujú správne")

    def test_keyboard_shortcuts(self):
        """Test klávesových skratiek."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Test save file
                cursor.save_file()
                self.mock_pyautogui.hotkey.assert_called_with("ctrl", "s")
                
                # Test copy
                cursor.copy()
                self.mock_pyautogui.hotkey.assert_called_with("ctrl", "c")
                
                # Test paste
                cursor.paste()
                self.mock_pyautogui.hotkey.assert_called_with("ctrl", "v")
                
                # Test undo
                cursor.undo()
                self.mock_pyautogui.hotkey.assert_called_with("ctrl", "z")
                
                print("✅ Klávesové skratky fungujú správne")

    def test_volume_control(self):
        """Test ovládania hlasitosti."""
        # Mock volume interface
        mock_volume = Mock()
        mock_volume.GetMasterVolumeLevelScalar.return_value = 0.5
        
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                with patch('voice_commands.cast', return_value=mock_volume):
                    from voice_commands import Cursor
                    
                    cursor = Cursor()
                    cursor.volume = mock_volume
                    
                    # Test volume up
                    cursor.volume_up()
                    mock_volume.SetMasterVolumeLevelScalar.assert_called_with(0.6, None)
                    
                    # Test volume down
                    cursor.volume_down()
                    mock_volume.SetMasterVolumeLevelScalar.assert_called_with(0.4, None)
                    
                    # Test volume set
                    cursor.volume_set(75)
                    mock_volume.SetMasterVolumeLevelScalar.assert_called_with(0.75, None)
                    
                    print("✅ Ovládanie hlasitosti funguje správne")

    def test_application_opening(self):
        """Test otvárania aplikácií."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Test opening webpage
                result = cursor.open_webpage("https://www.google.com")
                self.assertTrue(result)
                self.mock_webbrowser.open.assert_called_with("https://www.google.com")
                
                # Test opening application
                with patch('voice_commands.os.startfile') as mock_startfile:
                    result = cursor.open_application("notepad")
                    self.assertTrue(result)
                    mock_startfile.assert_called_with("notepad.exe")
                
                print("✅ Otváranie aplikácií funguje správne")

    def test_command_mapping(self):
        """Test mapovania príkazov."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Overenie, že command_map obsahuje očakávané príkazy
                expected_commands = [
                    "click", "right_click", "scroll_up", "scroll_down",
                    "volume_up", "volume_down", "save_file", "copy", "paste"
                ]
                
                for cmd in expected_commands:
                    self.assertIn(cmd, cursor.command_map)
                    self.assertIsInstance(cursor.command_map[cmd], tuple)
                    self.assertEqual(len(cursor.command_map[cmd]), 2)  # (function, response)
                
                print("✅ Mapovanie príkazov funguje správne")

    def test_voice_command_listener_setup(self):
        """Test nastavenia voice command listener."""
        # Mock recognizer a microphone
        mock_recognizer = Mock()
        mock_microphone = Mock()
        self.mock_sr.Recognizer.return_value = mock_recognizer
        self.mock_sr.Microphone.return_value = mock_microphone
        
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor, voice_command_listener
                
                cursor = Cursor()
                cursor.running = False  # Zastaviť po prvej iterácii
                queue = Queue()
                
                # Test, že sa listener spustí bez chyby
                try:
                    voice_command_listener(cursor, queue)
                    print("✅ Voice command listener sa spustil úspešne")
                except Exception as e:
                    self.fail(f"Voice command listener zlyhal: {e}")

    def test_gemini_integration(self):
        """Test integrácie s Gemini API."""
        mock_gemini_instance = Mock()
        self.mock_gemini_class.return_value = mock_gemini_instance
        
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Overenie, že Gemini API je inicializované
                self.assertIsNotNone(cursor.gemini)
                self.mock_gemini_class.assert_called_once()
                
                print("✅ Gemini integrácia funguje správne")

    def test_error_handling(self):
        """Test error handling."""
        with patch('voice_commands.json.load', return_value=self.mock_config):
            with patch('builtins.open', mock_open_config()):
                from voice_commands import Cursor
                
                cursor = Cursor()
                
                # Test s neplatnou URL
                with patch('voice_commands.webbrowser.open', side_effect=Exception("Test error")):
                    result = cursor.open_webpage("invalid_url")
                    self.assertFalse(result)
                
                # Test s neplatnou aplikáciou
                result = cursor.open_application("nonexistent_app")
                self.assertFalse(result)
                
                print("✅ Error handling funguje správne")

def mock_open_config():
    """Mock pre open() funkciu s konfiguráciou."""
    from unittest.mock import mock_open
    return mock_open(read_data=json.dumps({
        "voice": {"commands": {}},
        "mouse": {"scroll_amount_up": 3, "scroll_amount_down": -3}
    }))

def run_voice_commands_tests():
    """Spustí testy pre voice_commands modul."""
    print("🧪 Spúšťam testy pre voice_commands.py...")
    print("=" * 50)
    
    # Test importu
    try:
        with patch('voice_commands.sr'), patch('voice_commands.pyautogui'):
            with patch('voice_commands.webbrowser'), patch('voice_commands.gTTS'):
                with patch('voice_commands.playsound'), patch('voice_commands.AudioUtilities'):
                    with patch('voice_commands.GeminiAPI'), patch('voice_commands.process_voice_command'):
                        import voice_commands
                        print("✅ Import voice_commands.py - PASSED")
    except Exception as e:
        print(f"❌ Import voice_commands.py - FAILED: {e}")
        return False
    
    print("\n🎉 Všetky základné testy pre voice_commands prešli úspešne!")
    return True

if __name__ == "__main__":
    print("🚀 AirCursor Assistant - Test voice_commands.py")
    print("=" * 50)
    
    # Spustenie základných testov
    basic_success = run_voice_commands_tests()
    
    if basic_success:
        print("\n🧪 Spúšťam unit testy...")
        unittest.main(argv=[''], exit=False, verbosity=2)
    else:
        print("\n❌ Základné testy zlyhali, preskakujem unit testy")
        sys.exit(1)
