
# Weather patch pre main.py
# Pridajte tento kód do enhanced_voice_command_listener funkcie

def handle_weather_command(command):
    """Spracuje weather príkaz."""
    weather_responses = {
        "aké je počasie": "V Bratislave je momentálne 20°C, jasno. Vlhkosť 65%. (<PERSON><PERSON>)",
        "počasie dnes": "V Bratislave je momentálne 20°C, jasno. Vlhkosť 65%. (<PERSON><PERSON>)",
        "teplota vonku": "Aktuálna teplota je 20°C, pocitovo 22°C. (Demo údaje)",
        "aké bude počasie": "Predpoveď na najbližšie dni: Dnes slnečno 22°C, zajtra oblačno 18°C, pozajtra dážď 15°C. (Demo údaje)",
        "predpoveď počasia": "Predpoveď na najbližšie dni: Dnes slnečno 22°C, zajtra oblačno 18°C, pozajtra dážď 15°C. (Demo <PERSON>)"
    }
    
    command_lower = command.lower().strip()
    
    # Presná zhoda
    if command_lower in weather_responses:
        return True, weather_responses[command_lower]
    
    # Čiastočná zhoda
    for trigger, response in weather_responses.items():
        if any(word in command_lower for word in trigger.split()):
            if "počasie v" in command_lower:
                city = "Bratislava"  # Default
                if " v " in command_lower:
                    parts = command_lower.split(" v ")
                    if len(parts) > 1:
                        city = parts[1].strip().replace("?", "").replace(".", "").title()
                return True, f"V meste {city} je 20°C, jasno. Vlhkosť 65%. (Demo údaje)"
            return True, response
    
    return False, ""

# Použitie v enhanced_voice_command_listener:
# weather_handled, weather_response = handle_weather_command(command)
# if weather_handled:
#     queue.put(("tts", weather_response))
#     continue
