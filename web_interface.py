#!/usr/bin/env python3
"""
Webové rozhranie pre konfiguráciu AirCursor Assistant

Posky<PERSON>je webový server pre vzdialenú konfiguráciu a monitoring.
"""

import json
import os
import threading
import webbrowser
from pathlib import Path
from typing import Dict, Any, Optional
import logging

try:
    from flask import Flask, render_template_string, request, jsonify, send_from_directory
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    Flask = None

logger = logging.getLogger(__name__)

class WebInterface:
    """Webové rozhranie pre AirCursor Assistant."""
    
    def __init__(self, config_manager=None, plugin_manager=None, 
                 custom_commands_manager=None, port: int = 8080):
        if not FLASK_AVAILABLE:
            raise ImportError("Flask nie je nainštalovaný. Nainštalujte: pip install flask")
        
        self.app = Flask(__name__)
        self.config_manager = config_manager
        self.plugin_manager = plugin_manager
        self.custom_commands_manager = custom_commands_manager
        self.port = port
        self.server_thread = None
        self.running = False
        
        self._setup_routes()
    
    def _setup_routes(self):
        """Nastaví webové routes."""
        
        @self.app.route('/')
        def index():
            """Hlavná stránka."""
            return render_template_string(self._get_index_template())
        
        @self.app.route('/api/config', methods=['GET'])
        def get_config():
            """Vráti aktuálnu konfiguráciu."""
            if not self.config_manager:
                return jsonify({'error': 'Config manager nie je dostupný'}), 500
            
            try:
                config = {}
                sections = ['camera', 'hand_tracking', 'cursor', 'voice', 'calibration']
                
                for section in sections:
                    config[section] = self.config_manager.get(section, default={})
                
                return jsonify(config)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/config', methods=['POST'])
        def update_config():
            """Aktualizuje konfiguráciu."""
            if not self.config_manager:
                return jsonify({'error': 'Config manager nie je dostupný'}), 500
            
            try:
                data = request.get_json()
                
                for section, values in data.items():
                    if isinstance(values, dict):
                        for key, value in values.items():
                            self.config_manager.set(section, key, value)
                
                self.config_manager.save()
                return jsonify({'success': True})
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/plugins', methods=['GET'])
        def get_plugins():
            """Vráti zoznam pluginov."""
            if not self.plugin_manager:
                return jsonify({'error': 'Plugin manager nie je dostupný'}), 500
            
            try:
                plugins = []
                for plugin in self.plugin_manager.list_plugins():
                    plugins.append({
                        'name': plugin.name,
                        'version': plugin.version,
                        'description': plugin.description,
                        'author': plugin.author,
                        'enabled': plugin.enabled,
                        'loaded': plugin.loaded
                    })
                
                return jsonify(plugins)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/plugins/<plugin_name>/enable', methods=['POST'])
        def enable_plugin(plugin_name):
            """Povolí plugin."""
            if not self.plugin_manager:
                return jsonify({'error': 'Plugin manager nie je dostupný'}), 500
            
            try:
                success = self.plugin_manager.enable_plugin(plugin_name)
                return jsonify({'success': success})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/plugins/<plugin_name>/disable', methods=['POST'])
        def disable_plugin(plugin_name):
            """Zakáže plugin."""
            if not self.plugin_manager:
                return jsonify({'error': 'Plugin manager nie je dostupný'}), 500
            
            try:
                success = self.plugin_manager.disable_plugin(plugin_name)
                return jsonify({'success': success})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/commands', methods=['GET'])
        def get_custom_commands():
            """Vráti vlastné príkazy."""
            if not self.custom_commands_manager:
                return jsonify({'error': 'Custom commands manager nie je dostupný'}), 500
            
            try:
                commands = []
                for cmd in self.custom_commands_manager.list_commands():
                    commands.append({
                        'name': cmd.name,
                        'triggers': cmd.triggers,
                        'action_type': cmd.action_type,
                        'description': cmd.description,
                        'usage_count': cmd.usage_count
                    })
                
                return jsonify(commands)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/commands', methods=['POST'])
        def add_custom_command():
            """Pridá nový vlastný príkaz."""
            if not self.custom_commands_manager:
                return jsonify({'error': 'Custom commands manager nie je dostupný'}), 500
            
            try:
                data = request.get_json()
                
                from custom_voice_commands import CustomCommand
                
                command = CustomCommand(
                    name=data['name'],
                    triggers=data['triggers'],
                    action_type=data['action_type'],
                    action_data=data['action_data'],
                    description=data.get('description', '')
                )
                
                success = self.custom_commands_manager.add_command(command)
                return jsonify({'success': success})
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/commands/<command_name>', methods=['DELETE'])
        def delete_custom_command(command_name):
            """Odstráni vlastný príkaz."""
            if not self.custom_commands_manager:
                return jsonify({'error': 'Custom commands manager nie je dostupný'}), 500
            
            try:
                success = self.custom_commands_manager.remove_command(command_name)
                return jsonify({'success': success})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            """Vráti status aplikácie."""
            try:
                status = {
                    'web_interface': True,
                    'config_manager': self.config_manager is not None,
                    'plugin_manager': self.plugin_manager is not None,
                    'custom_commands_manager': self.custom_commands_manager is not None,
                    'loaded_plugins': len(self.plugin_manager.list_loaded_plugins()) if self.plugin_manager else 0,
                    'custom_commands': len(self.custom_commands_manager.list_commands()) if self.custom_commands_manager else 0
                }
                
                return jsonify(status)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def _get_index_template(self) -> str:
        """Vráti HTML template pre hlavnú stránku."""
        return '''
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirCursor Assistant - Webové rozhranie</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            color: #718096;
            font-size: 1.2em;
        }
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h2 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status.online { background: #48bb78; color: white; }
        .status.offline { background: #f56565; color: white; }
        .loading {
            text-align: center;
            padding: 20px;
            color: #718096;
        }
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success {
            background: #c6f6d5;
            color: #2f855a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AirCursor Assistant</h1>
            <p>Webové rozhranie pre konfiguráciu a správu</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h2>📊 Status systému</h2>
                <div id="status-content" class="loading">Načítavam...</div>
            </div>
            
            <div class="card">
                <h2>⚙️ Konfigurácia</h2>
                <div id="config-content" class="loading">Načítavam...</div>
            </div>
            
            <div class="card">
                <h2>🔌 Pluginy</h2>
                <div id="plugins-content" class="loading">Načítavam...</div>
            </div>
            
            <div class="card">
                <h2>🎤 Vlastné príkazy</h2>
                <div id="commands-content" class="loading">Načítavam...</div>
            </div>
        </div>
    </div>

    <script>
        // Načítanie status
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                const statusHtml = `
                    <p><strong>Webové rozhranie:</strong> <span class="status online">Online</span></p>
                    <p><strong>Config Manager:</strong> <span class="status ${data.config_manager ? 'online' : 'offline'}">${data.config_manager ? 'Aktívny' : 'Neaktívny'}</span></p>
                    <p><strong>Plugin Manager:</strong> <span class="status ${data.plugin_manager ? 'online' : 'offline'}">${data.plugin_manager ? 'Aktívny' : 'Neaktívny'}</span></p>
                    <p><strong>Načítané pluginy:</strong> ${data.loaded_plugins}</p>
                    <p><strong>Vlastné príkazy:</strong> ${data.custom_commands}</p>
                `;
                document.getElementById('status-content').innerHTML = statusHtml;
            })
            .catch(error => {
                document.getElementById('status-content').innerHTML = '<div class="error">Chyba pri načítaní status</div>';
            });

        // Načítanie konfigurácie
        fetch('/api/config')
            .then(response => response.json())
            .then(data => {
                let configHtml = '<p>Konfigurácia je dostupná cez API.</p>';
                configHtml += '<button class="btn" onclick="alert(\'Konfigurácia bude dostupná v budúcej verzii\')">Upraviť konfiguráciu</button>';
                document.getElementById('config-content').innerHTML = configHtml;
            })
            .catch(error => {
                document.getElementById('config-content').innerHTML = '<div class="error">Chyba pri načítaní konfigurácie</div>';
            });

        // Načítanie pluginov
        fetch('/api/plugins')
            .then(response => response.json())
            .then(data => {
                let pluginsHtml = '';
                if (data.length === 0) {
                    pluginsHtml = '<p>Žiadne pluginy nie sú nainštalované.</p>';
                } else {
                    data.forEach(plugin => {
                        pluginsHtml += `
                            <div style="border: 1px solid #e2e8f0; padding: 15px; margin: 10px 0; border-radius: 8px;">
                                <h3>${plugin.name} v${plugin.version}</h3>
                                <p>${plugin.description}</p>
                                <p><strong>Autor:</strong> ${plugin.author}</p>
                                <p><strong>Status:</strong> <span class="status ${plugin.enabled ? 'online' : 'offline'}">${plugin.enabled ? 'Povolený' : 'Zakázaný'}</span></p>
                            </div>
                        `;
                    });
                }
                document.getElementById('plugins-content').innerHTML = pluginsHtml;
            })
            .catch(error => {
                document.getElementById('plugins-content').innerHTML = '<div class="error">Chyba pri načítaní pluginov</div>';
            });

        // Načítanie vlastných príkazov
        fetch('/api/commands')
            .then(response => response.json())
            .then(data => {
                let commandsHtml = '';
                if (data.length === 0) {
                    commandsHtml = '<p>Žiadne vlastné príkazy nie sú definované.</p>';
                } else {
                    data.forEach(cmd => {
                        commandsHtml += `
                            <div style="border: 1px solid #e2e8f0; padding: 15px; margin: 10px 0; border-radius: 8px;">
                                <h3>${cmd.name}</h3>
                                <p>${cmd.description}</p>
                                <p><strong>Triggery:</strong> ${cmd.triggers.join(', ')}</p>
                                <p><strong>Typ:</strong> ${cmd.action_type}</p>
                                <p><strong>Použité:</strong> ${cmd.usage_count}x</p>
                            </div>
                        `;
                    });
                }
                commandsHtml += '<button class="btn" onclick="alert(\'Pridávanie príkazov bude dostupné v budúcej verzii\')">Pridať príkaz</button>';
                document.getElementById('commands-content').innerHTML = commandsHtml;
            })
            .catch(error => {
                document.getElementById('commands-content').innerHTML = '<div class="error">Chyba pri načítaní príkazov</div>';
            });
    </script>
</body>
</html>
        '''
    
    def start(self, open_browser: bool = True):
        """Spustí webový server."""
        if self.running:
            logger.warning("Webový server už beží")
            return
        
        def run_server():
            try:
                self.app.run(host='0.0.0.0', port=self.port, debug=False, use_reloader=False)
            except Exception as e:
                logger.error(f"Chyba pri spúšťaní webového servera: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.running = True
        
        logger.info(f"Webový server spustený na http://localhost:{self.port}")
        
        if open_browser:
            threading.Timer(1.0, lambda: webbrowser.open(f"http://localhost:{self.port}")).start()
    
    def stop(self):
        """Zastaví webový server."""
        self.running = False
        logger.info("Webový server zastavený")

if __name__ == "__main__":
    if not FLASK_AVAILABLE:
        print("❌ Flask nie je nainštalovaný. Nainštalujte: pip install flask")
        exit(1)
    
    # Test webového rozhrania
    web = WebInterface()
    web.start()
    
    try:
        input("Stlačte Enter pre zastavenie servera...")
    except KeyboardInterrupt:
        pass
    finally:
        web.stop()
